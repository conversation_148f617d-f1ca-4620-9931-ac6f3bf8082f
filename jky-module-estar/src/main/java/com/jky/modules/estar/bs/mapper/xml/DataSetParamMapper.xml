<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.modules.estar.bs.mapper.DataSetParamMapper">

  <resultMap type="com.jky.modules.estar.bs.param.DataSetParam" id="DataSetParamMap">
        <!--jdbcType="{column.columnType}"-->
        <result property="id" column="id"  />
        <result property="setCode" column="set_code"  />
        <result property="paramName" column="param_name"  />
        <result property="paramDesc" column="param_desc"  />
        <result property="paramType" column="param_type"  />
        <result property="sampleItem" column="sample_item"  />
        <result property="requiredFlag" column="required_flag"  />
        <result property="validationRules" column="validation_rules"  />
        <result property="orderNum" column="order_num"  />
        <result property="createBy" column="create_by"  />
        <result property="createTime" column="create_time"  />
        <result property="updateBy" column="update_by"  />
        <result property="updateTime" column="update_time"  />

    </resultMap>

    <sql id="Base_Column_List">
        id,set_code,param_name,param_desc,param_type,sample_item,required_flag,validation_rules,order_num,create_by,create_time,update_by,update_time
    </sql>

    <!--自定义sql -->
</mapper>