<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.modules.estar.tw.mapper.CommMapper">

	<select id="customQueryItem" parameterType="String" resultType="java.util.Map">
         <![CDATA[
            SELECT *  FROM (${sqlContent}) obj
          ]]>
    </select>
    <select id="customQueryItemOne" parameterType="String" resultType="java.util.Map">
         <![CDATA[
            SELECT *  FROM (${sqlContent}) obj
          ]]>
    </select>

</mapper> 