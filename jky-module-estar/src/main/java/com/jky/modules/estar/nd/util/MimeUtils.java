package com.jky.modules.estar.nd.util;

import java.util.HashMap;
import java.util.Map;

public class MimeUtils {

	public static String getMime(String suffix){
		return map.get(suffix);
	}
	private static Map<String,String> map=new HashMap<String,String>();
	static{
		map.put("323", "text/h323");
		map.put("3gp", "video/3gpp");
		map.put("aab", "application/x-authoware-bin");
		map.put("aam", "application/x-authoware-map");
		map.put("aas", "application/x-authoware-seg");
		map.put("acx", "application/internet-property-stream");
		map.put("ai", "application/postscript");
		map.put("aif", "audio/x-aiff");
		map.put("aifc", "audio/x-aiff");
		map.put("aiff", "audio/x-aiff");
		map.put("als", "audio/X-Alpha5");
		map.put("amc", "application/x-mpeg");
		map.put("ani", "application/octet-stream");
		map.put("apk", "application/vnd.android.package-archive");
		map.put("asc", "text/plain");
		map.put("asd", "application/astound");
		map.put("asf", "video/x-ms-asf");
		map.put("asn", "application/astound");
		map.put("asp", "application/x-asap");
		map.put("asr", "video/x-ms-asf");
		map.put("asx", "video/x-ms-asf");
		map.put("au", "audio/basic");
		map.put("avb", "application/octet-stream");
		map.put("avi", "video/x-msvideo");
		map.put("awb", "audio/amr-wb");
		map.put("axs", "application/olescript");
		map.put("bas", "text/plain");
		map.put("bcpio", "application/x-bcpio");
		map.put("bin ", "application/octet-stream");
		map.put("bld", "application/bld");
		map.put("bld2", "application/bld2");
		map.put("bmp", "image/bmp");
		map.put("bpk", "application/octet-stream");
		map.put("bz2", "application/x-bzip2");
		map.put("c", "text/plain");
		map.put("cal", "image/x-cals");
		map.put("cat", "application/vnd.ms-pkiseccat");
		map.put("ccn", "application/x-cnc");
		map.put("cco", "application/x-cocoa");
		map.put("cdf", "application/x-cdf");
		map.put("cer", "application/x-x509-ca-cert");
		map.put("cgi", "magnus-internal/cgi");
		map.put("chat", "application/x-chat");
		map.put("class", "application/octet-stream");
		map.put("clp", "application/x-msclip");
		map.put("cmx", "image/x-cmx");
		map.put("co", "application/x-cult3d-object");
		map.put("cod", "image/cis-cod");
		map.put("conf", "text/plain");
		map.put("cpio", "application/x-cpio");
		map.put("cpp", "text/plain");
		map.put("cpt", "application/mac-compactpro");
		map.put("crd", "application/x-mscardfile");
		map.put("crl", "application/pkix-crl");
		map.put("crt", "application/x-x509-ca-cert");
		map.put("csh", "application/x-csh");
		map.put("csm", "chemical/x-csml");
		map.put("csml", "chemical/x-csml");
		map.put("css", "text/css");
		map.put("cur", "application/octet-stream");
		map.put("dcm", "x-lml/x-evm");
		map.put("dcr", "application/x-director");
		map.put("dcx", "image/x-dcx");
		map.put("der", "application/x-x509-ca-cert");
		map.put("dhtml", "text/html");
		map.put("dir", "application/x-director");
		map.put("dll", "application/x-msdownload");
		map.put("dmg", "application/octet-stream");
		map.put("dms", "application/octet-stream");
		map.put("doc", "application/msword");
		map.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
		map.put("dot", "application/msword");
		map.put("dvi", "application/x-dvi");
		map.put("dwf", "drawing/x-dwf");
		map.put("dwg", "application/x-autocad");
		map.put("dxf", "application/x-autocad");
		map.put("dxr", "application/x-director");
		map.put("ebk", "application/x-expandedbook");
		map.put("emb", "chemical/x-embl-dl-nucleotide");
		map.put("embl", "chemical/x-embl-dl-nucleotide");
		map.put("eps", "application/postscript");
		map.put("epub", "application/epub+zip");
		map.put("eri", "image/x-eri");
		map.put("es", "audio/echospeech");
		map.put("esl", "audio/echospeech");
		map.put("etc", "application/x-earthtime");
		map.put("etx", "text/x-setext");
		map.put("evm", "x-lml/x-evm");
		map.put("evy", "application/envoy");
		map.put("exe", "application/octet-stream");
		map.put("fh4", "image/x-freehand");
		map.put("fh5", "image/x-freehand");
		map.put("fhc", "image/x-freehand");
		map.put("fif", "application/fractals");
		map.put("flr", "x-world/x-vrml");
		map.put("flv", "flv-application/octet-stream");
		map.put("fm", "application/x-maker");
		map.put("fpx", "image/x-fpx");
		map.put("fvi", "video/isivideo");
		map.put("gau", "chemical/x-gaussian-input");
		map.put("gca", "application/x-gca-compressed");
		map.put("gdb", "x-lml/x-gdb");
		map.put("gif", "image/gif");
		map.put("gps", "application/x-gps");
		map.put("gtar", "application/x-gtar");
		map.put("gz", "application/x-gzip");
		map.put("h", "text/plain");
		map.put("hdf", "application/x-hdf");
		map.put("hdm", "text/x-hdml");
		map.put("hdml", "text/x-hdml");
		map.put("hlp", "application/winhlp");
		map.put("hqx", "application/mac-binhex40");
		map.put("hta", "application/hta");
		map.put("htc", "text/x-component");
		map.put("htm", "text/html");
		map.put("html", "text/html");
		map.put("hts", "text/html");
		map.put("htt", "text/webviewhtml");
		map.put("ice", "x-conference/x-cooltalk");
		map.put("ico", "image/x-icon");
		map.put("ief", "image/ief");
		map.put("ifm", "image/gif");
		map.put("ifs", "image/ifs");
		map.put("iii", "application/x-iphone");
		map.put("imy", "audio/melody");
		map.put("ins", "application/x-internet-signup");
		map.put("ips", "application/x-ipscript");
		map.put("ipx", "application/x-ipix");
		map.put("isp", "application/x-internet-signup");
		map.put("it", "audio/x-mod");
		map.put("itz", "audio/x-mod");
		map.put("ivr", "i-world/i-vrml");
		map.put("j2k", "image/j2k");
		map.put("jad", "text/vnd.sun.j2me.app-descriptor");
		map.put("jam", "application/x-jam");
		map.put("jar", "application/java-archive");
		map.put("java", "text/plain");
		map.put("jfif", "image/pipeg");
		map.put("jnlp", "application/x-java-jnlp-file");
		map.put("jpe", "image/jpeg");
		map.put("jpeg", "image/jpeg");
		map.put("jpg", "image/jpeg");
		map.put("jpz", "image/jpeg");
		map.put("js", "application/x-javascript");
		map.put("jwc", "application/jwc");
		map.put("kjx", "application/x-kjx");
		map.put("lak", "x-lml/x-lak");
		map.put("latex", "application/x-latex");
		map.put("lcc", "application/fastman");
		map.put("lcl", "application/x-digitalloca");
		map.put("lcr", "application/x-digitalloca");
		map.put("lgh", "application/lgh");
		map.put("lha", "application/octet-stream");
		map.put("lml", "x-lml/x-lml");
		map.put("lmlpack", "x-lml/x-lmlpack");
		map.put("log", "text/plain");
		map.put("lsf", "video/x-la-asf");
		map.put("lsx", "video/x-la-asf");
		map.put("lzh", "application/octet-stream");
		map.put("m13", "application/x-msmediaview");
		map.put("m14", "application/x-msmediaview");
		map.put("m15", "audio/x-mod");
		map.put("m3u", "audio/x-mpegurl");
		map.put("m3url", "audio/x-mpegurl");
		map.put("m4a", "audio/mp4a-latm");
		map.put("m4b", "audio/mp4a-latm");
		map.put("m4p", "audio/mp4a-latm");
		map.put("m4u", "video/vnd.mpegurl");
		map.put("m4v", "video/x-m4v");
		map.put("ma1", "audio/ma1");
		map.put("ma2", "audio/ma2");
		map.put("ma3", "audio/ma3");
		map.put("ma5", "audio/ma5");
		map.put("man", "application/x-troff-man");
		map.put("map", "magnus-internal/imagemap");
		map.put("mbd", "application/mbedlet");
		map.put("mct", "application/x-mascot");
		map.put("mdb", "application/x-msaccess");
		map.put("mdz", "audio/x-mod");
		map.put("me", "application/x-troff-me");
		map.put("mel", "text/x-vmel");
		map.put("mht", "message/rfc822");
		map.put("mhtml", "message/rfc822");
		map.put("mi", "application/x-mif");
		map.put("mid", "audio/mid");
		map.put("midi", "audio/midi");
		map.put("mif", "application/x-mif");
		map.put("mil", "image/x-cals");
		map.put("mio", "audio/x-mio");
		map.put("mmf", "application/x-skt-lbs");
		map.put("mng", "video/x-mng");
		map.put("mny", "application/x-msmoney");
		map.put("moc", "application/x-mocha");
		map.put("mocha", "application/x-mocha");
		map.put("mod", "audio/x-mod");
		map.put("mof", "application/x-yumekara");
		map.put("mol", "chemical/x-mdl-molfile");
		map.put("mop", "chemical/x-mopac-input");
		map.put("mov", "video/quicktime");
		map.put("movie", "video/x-sgi-movie");
		map.put("mp2", "video/mpeg");
		map.put("mp3", "audio/mpeg");
		map.put("mp4", "video/mp4");
		map.put("mpa", "video/mpeg");
		map.put("mpc", "application/vnd.mpohun.certificate");
		map.put("mpe", "video/mpeg");
		map.put("mpeg", "video/mpeg");
		map.put("mpg", "video/mpeg");
		map.put("mpg4", "video/mp4");
		map.put("mpga", "audio/mpeg");
		map.put("mpn", "application/vnd.mophun.application");
		map.put("mpp", "application/vnd.ms-project");
		map.put("mps", "application/x-mapserver");
		map.put("mpv2", "video/mpeg");
		map.put("mrl", "text/x-mrml");
		map.put("mrm", "application/x-mrm");
		map.put("ms", "application/x-troff-ms");
		map.put("msg", "application/vnd.ms-outlook");
		map.put("mts", "application/metastream");
		map.put("mtx", "application/metastream");
		map.put("mtz", "application/metastream");
		map.put("mvb", "application/x-msmediaview");
		map.put("mzv", "application/metastream");
		map.put("nar", "application/zip");
		map.put("nbmp", "image/nbmp");
		map.put("nc", "application/x-netcdf");
		map.put("ndb", "x-lml/x-ndb");
		map.put("ndwn", "application/ndwn");
		map.put("nif", "application/x-nif");
		map.put("nmz", "application/x-scream");
		map.put("nokia-op-logo", "image/vnd.nok-oplogo-color");
		map.put("npx", "application/x-netfpx");
		map.put("nsnd", "audio/nsnd");
		map.put("nva", "application/x-neva1");
		map.put("nws", "message/rfc822");
		map.put("oda", "application/oda");
		map.put("ogg", "audio/ogg");
		map.put("oom", "application/x-AtlasMate-Plugin");
		map.put("p10", "application/pkcs10");
		map.put("p12", "application/x-pkcs12");
		map.put("p7b", "application/x-pkcs7-certificates");
		map.put("p7c", "application/x-pkcs7-mime");
		map.put("p7m", "application/x-pkcs7-mime");
		map.put("p7r", "application/x-pkcs7-certreqresp");
		map.put("p7s", "application/x-pkcs7-signature");
		map.put("pac", "audio/x-pac");
		map.put("pae", "audio/x-epac");
		map.put("pan", "application/x-pan");
		map.put("pbm", "image/x-portable-bitmap");
		map.put("pcx", "image/x-pcx");
		map.put("pda", "image/x-pda");
		map.put("pdb", "chemical/x-pdb");
		map.put("pdf", "application/pdf");
		map.put("pfr", "application/font-tdpfr");
		map.put("pfx", "application/x-pkcs12");
		map.put("pgm", "image/x-portable-graymap");
		map.put("pict", "image/x-pict");
		map.put("pko", "application/ynd.ms-pkipko");
		map.put("pm", "application/x-perl");
		map.put("pma", "application/x-perfmon");
		map.put("pmc", "application/x-perfmon");
		map.put("pmd", "application/x-pmd");
		map.put("pml", "application/x-perfmon");
		map.put("pmr", "application/x-perfmon");
		map.put("pmw", "application/x-perfmon");
		map.put("png", "image/png");
		map.put("pnm", "image/x-portable-anymap");
		map.put("pnz", "image/png");
		map.put("pot,", "application/vnd.ms-powerpoint");
		map.put("ppm", "image/x-portable-pixmap");
		map.put("pps", "application/vnd.ms-powerpoint");
		map.put("ppt", "application/vnd.ms-powerpoint");
		map.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
		map.put("pqf", "application/x-cprplayer");
		map.put("pqi", "application/cprplayer");
		map.put("prc", "application/x-prc");
		map.put("prf", "application/pics-rules");
		map.put("prop", "text/plain");
		map.put("proxy", "application/x-ns-proxy-autoconfig");
		map.put("ps", "application/postscript");
		map.put("ptlk", "application/listenup");
		map.put("pub", "application/x-mspublisher");
		map.put("pvx", "video/x-pv-pvx");
		map.put("qcp", "audio/vnd.qcelp");
		map.put("qt", "video/quicktime");
		map.put("qti", "image/x-quicktime");
		map.put("qtif", "image/x-quicktime");
		map.put("r3t", "text/vnd.rn-realtext3d");
		map.put("ra", "audio/x-pn-realaudio");
		map.put("ram", "audio/x-pn-realaudio");
		map.put("rar", "application/octet-stream");
		map.put("ras", "image/x-cmu-raster");
		map.put("rc", "text/plain");
		map.put("rdf", "application/rdf+xml");
		map.put("rf", "image/vnd.rn-realflash");
		map.put("rgb", "image/x-rgb");
		map.put("rlf", "application/x-richlink");
		map.put("rm", "audio/x-pn-realaudio");
		map.put("rmf", "audio/x-rmf");
		map.put("rmi", "audio/mid");
		map.put("rmm", "audio/x-pn-realaudio");
		map.put("rmvb", "audio/x-pn-realaudio");
		map.put("rnx", "application/vnd.rn-realplayer");
		map.put("roff", "application/x-troff");
		map.put("rp", "image/vnd.rn-realpix");
		map.put("rpm", "audio/x-pn-realaudio-plugin");
		map.put("rt", "text/vnd.rn-realtext");
		map.put("rte", "x-lml/x-gps");
		map.put("rtf", "application/rtf");
		map.put("rtg", "application/metastream");
		map.put("rtx", "text/richtext");
		map.put("rv", "video/vnd.rn-realvideo");
		map.put("rwc", "application/x-rogerwilco");
		map.put("s3m", "audio/x-mod");
		map.put("s3z", "audio/x-mod");
		map.put("sca", "application/x-supercard");
		map.put("scd", "application/x-msschedule");
		map.put("sct", "text/scriptlet");
		map.put("sdf", "application/e-score");
		map.put("sea", "application/x-stuffit");
		map.put("setpay", "application/set-payment-initiation");
		map.put("setreg", "application/set-registration-initiation");
		map.put("sgm", "text/x-sgml");
		map.put("sgml", "text/x-sgml");
		map.put("sh", "application/x-sh");
		map.put("shar", "application/x-shar");
		map.put("shtml", "magnus-internal/parsed-html");
		map.put("shw", "application/presentations");
		map.put("si6", "image/si6");
		map.put("si7", "image/vnd.stiwap.sis");
		map.put("si9", "image/vnd.lgtwap.sis");
		map.put("sis", "application/vnd.symbian.install");
		map.put("sit", "application/x-stuffit");
		map.put("skd", "application/x-Koan");
		map.put("skm", "application/x-Koan");
		map.put("skp", "application/x-Koan");
		map.put("skt", "application/x-Koan");
		map.put("slc", "application/x-salsa");
		map.put("smd", "audio/x-smd");
		map.put("smi", "application/smil");
		map.put("smil", "application/smil");
		map.put("smp", "application/studiom");
		map.put("smz", "audio/x-smd");
		map.put("snd", "audio/basic");
		map.put("spc", "application/x-pkcs7-certificates");
		map.put("spl", "application/futuresplash");
		map.put("spr", "application/x-sprite");
		map.put("sprite", "application/x-sprite");
		map.put("sdp", "application/sdp");
		map.put("spt", "application/x-spt");
		map.put("src", "application/x-wais-source");
		map.put("sst", "application/vnd.ms-pkicertstore");
		map.put("stk", "application/hyperstudio");
		map.put("stl", "application/vnd.ms-pkistl");
		map.put("stm", "text/html");
		map.put("svg", "image/svg+xml");
		map.put("sv4cpio", "application/x-sv4cpio");
		map.put("sv4crc", "application/x-sv4crc");
		map.put("svf", "image/vnd");
		map.put("svg", "image/svg+xml");
		map.put("svh", "image/svh");
		map.put("svr", "x-world/x-svr");
		map.put("swf", "application/x-shockwave-flash");
		map.put("swfl", "application/x-shockwave-flash");
		map.put("t", "application/x-troff");
		map.put("tad", "application/octet-stream");
		map.put("talk", "text/x-speech");
		map.put("tar", "application/x-tar");
		map.put("taz", "application/x-tar");
		map.put("tbp", "application/x-timbuktu");
		map.put("tbt", "application/x-timbuktu");
		map.put("tcl", "application/x-tcl");
		map.put("tex", "application/x-tex");
		map.put("texi", "application/x-texinfo");
		map.put("texinfo", "application/x-texinfo");
		map.put("tgz", "application/x-compressed");
		map.put("thm", "application/vnd.eri.thm");
		map.put("tif", "image/tiff");
		map.put("tiff", "image/tiff");
		map.put("tki", "application/x-tkined");
		map.put("tkined", "application/x-tkined");
		map.put("toc", "application/toc");
		map.put("toy", "image/toy");
		map.put("tr", "application/x-troff");
		map.put("trk", "x-lml/x-gps");
		map.put("trm", "application/x-msterminal");
		map.put("tsi", "audio/tsplayer");
		map.put("tsp", "application/dsptype");
		map.put("tsv", "text/tab-separated-values");
		map.put("ttf", "application/octet-stream");
		map.put("ttz", "application/t-time");
		map.put("txt", "text/plain");
		map.put("uls", "text/iuls");
		map.put("ult", "audio/x-mod");
		map.put("ustar", "application/x-ustar");
		map.put("uu", "application/x-uuencode");
		map.put("uue", "application/x-uuencode");
		map.put("vcd", "application/x-cdlink");
		map.put("vcf", "text/x-vcard");
		map.put("vdo", "video/vdo");
		map.put("vib", "audio/vib");
		map.put("viv", "video/vivo");
		map.put("vivo", "video/vivo");
		map.put("vmd", "application/vocaltec-media-desc");
		map.put("vmf", "application/vocaltec-media-file");
		map.put("vmi", "application/x-dreamcast-vms-info");
		map.put("vms", "application/x-dreamcast-vms");
		map.put("vox", "audio/voxware");
		map.put("vqe", "audio/x-twinvq-plugin");
		map.put("vqf", "audio/x-twinvq");
		map.put("vql", "audio/x-twinvq");
		map.put("vre", "x-world/x-vream");
		map.put("vrml", "x-world/x-vrml");
		map.put("vrt", "x-world/x-vrt");
		map.put("vrw", "x-world/x-vream");
		map.put("vts", "workbook/formulaone");
		map.put("wav", "audio/x-wav");
		map.put("wax", "audio/x-ms-wax");
		map.put("wbmp", "image/vnd.wap.wbmp");
		map.put("wcm", "application/vnd.ms-works");
		map.put("wdb", "application/vnd.ms-works");
		map.put("web", "application/vnd.xara");
		map.put("wi", "image/wavelet");
		map.put("wis", "application/x-InstallShield");
		map.put("wks", "application/vnd.ms-works");
		map.put("wm", "video/x-ms-wm");
		map.put("wma", "audio/x-ms-wma");
		map.put("wmd", "application/x-ms-wmd");
		map.put("wmf", "application/x-msmetafile");
		map.put("wml", "text/vnd.wap.wml");
		map.put("wmlc", "application/vnd.wap.wmlc");
		map.put("wmls", "text/vnd.wap.wmlscript");
		map.put("wmlsc", "application/vnd.wap.wmlscriptc");
		map.put("wmlscript", "text/vnd.wap.wmlscript");
		map.put("wmv", "audio/x-ms-wmv");
		map.put("wmx", "video/x-ms-wmx");
		map.put("wmz", "application/x-ms-wmz");
		map.put("wpng", "image/x-up-wpng");
		map.put("wps", "application/vnd.ms-works");
		map.put("wpt", "x-lml/x-gps");
		map.put("wri", "application/x-mswrite");
		map.put("wrl", "x-world/x-vrml");
		map.put("wrz", "x-world/x-vrml");
		map.put("ws", "text/vnd.wap.wmlscript");
		map.put("wsc", "application/vnd.wap.wmlscriptc");
		map.put("wv", "video/wavelet");
		map.put("wvx", "video/x-ms-wvx");
		map.put("wxl", "application/x-wxl");
		map.put("x-gzip", "application/x-gzip");
		map.put("xaf", "x-world/x-vrml");
		map.put("xar", "application/vnd.xara");
		map.put("xbm", "image/x-xbitmap");
		map.put("xdm", "application/x-xdma");
		map.put("xdma", "application/x-xdma");
		map.put("xdw", "application/vnd.fujixerox.docuworks");
		map.put("xht", "application/xhtml+xml");
		map.put("xhtm", "application/xhtml+xml");
		map.put("xhtml", "application/xhtml+xml");
		map.put("xla", "application/vnd.ms-excel");
		map.put("xlc", "application/vnd.ms-excel");
		map.put("xll", "application/x-excel");
		map.put("xlm", "application/vnd.ms-excel");
		map.put("xls", "application/vnd.ms-excel");
		map.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		map.put("xlt", "application/vnd.ms-excel");
		map.put("xlw", "application/vnd.ms-excel");
		map.put("xm", "audio/x-mod");
		map.put("xml","text/plain");
		map.put("xml","application/xml");
		map.put("xmz", "audio/x-mod");
		map.put("xof", "x-world/x-vrml");
		map.put("xpi", "application/x-xpinstall");
		map.put("xpm", "image/x-xpixmap");
		map.put("xsit", "text/xml");
		map.put("xsl", "text/xml");
		map.put("xul", "text/xul");
		map.put("xwd", "image/x-xwindowdump");
		map.put("xyz", "chemical/x-pdb");
		map.put("yz1", "application/x-yz1");
		map.put("z", "application/x-compress");
		map.put("zac", "application/x-zaurus-zac");
		map.put("zip", "application/zip");
		map.put("json", "application/json");
	}
}
