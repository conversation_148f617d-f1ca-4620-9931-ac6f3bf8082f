<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.modules.estar.tw.mapper.TwTaskMapper">

<select id="selectTaskListByParam" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM tw_task a WHERE  1=1
		<if test="params.stageId != null and params.stageId != ''">
			AND a.stage_id = #{params.stageId}
		</if>
		<if test="params.pid != null and params.pid != ''">
			AND a.pid = #{params.pid}
		</if>
		<if test="params.keyword != null and params.keyword != ''">
			AND a.name like concat('%',#{params.keyword},'%')
		</if>
		<if test="params.projectId != null and params.projectId != ''">
			AND a.project_id = #{params.projectId}
		</if>
		<if test="params.deleted != null and params.deleted != ''">
			AND a.deleted = #{params.deleted}
		</if>
		order by a.sort asc,a.id asc
	</select>
</mapper>