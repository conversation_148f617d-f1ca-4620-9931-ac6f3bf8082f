#code_generate_project_path
project_path=E:\\STS\\workspace\\jky-boot
#bussi_package[User defined]
bussi_package=com.jky.modules.estar


#default code path
#source_root_package=src
#webroot_package=WebRoot

#maven code path
source_root_package=src.main.java
webroot_package=src.main.webapp

#ftl resource url
templatepath=/jky/code-template
system_encoding=utf-8

#db Table id [User defined] 
db_table_id=id

#db convert flag[true/false]
db_filed_convert=true

#page Search Field num [User defined]
page_search_filed_num=1
#page_filter_fields
page_filter_fields=create_time,create_by,update_time,update_by
exclude_table=act_,ext_act_,design_,onl_,sys_,qrtz_
