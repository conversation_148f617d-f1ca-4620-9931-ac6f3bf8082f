//package org.jeecg.modules.system.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.service.IService;
//import liquibase.pro.packaged.S;
//import org.jeecg.common.api.vo.Result;
//import org.jeecg.modules.system.entity.SysUser;
//import org.jeecg.modules.system.model.DepartIdModel;
//
//import java.util.List;
//import java.util.Map;
//
//
///**
// * 添加用户
// */
//public interface ISysUserService2 extends IService<SysUser> {
//
//    IPage<SysUser> findList(SysUser user, Integer pageNo, Integer pageSize) throws Exception;
//
//    SysUser add(SysUser sysUser,String selectedRoles, String selectedDeparts) throws Exception;
//
//    Result<SysUser> edit(SysUser user, String selectedRoles, String selectedDeparts) throws Exception;
//
//    SysUser findById(String id) throws Exception;
//
//    void delById(String id) throws Exception;
//
//    void delByIds(List<String> ids) throws Exception;
//
//    void updateStatus(String[] ids,String status) throws Exception;
//
//    public SysUser getUserByName(String username);
//
//
//    /**
//     * 修改密码
//     *
//     * @param sysUser
//     * @return
//     */
//    public Result<?> changePassword(SysUser sysUser);
//
//    /**
//     * 根据 userIds查询，查询用户所属部门的名称（多个部门名逗号隔开）
//     * @param
//     * @return
//     */
//    public Map<String,String> getDepNamesByUserIds(List<String> userIds);
//
//    /**
//     * 重置密码
//     *
//     * @return
//     */
//    public Result<?> resetPassword(JSONObject json);
//
//}
