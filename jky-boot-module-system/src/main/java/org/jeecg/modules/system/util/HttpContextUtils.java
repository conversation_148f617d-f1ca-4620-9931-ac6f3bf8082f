package org.jeecg.modules.system.util;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @project jky-boot
 * @description
 * @date 2023/11/23
 */
public class HttpContextUtils {
    /**

     * 获取query参数

     * @param request

     * @return

     */

    public static Map getParameterMapAll(HttpServletRequest request) {

        Enumeration parameters = request.getParameterNames();

        Map params = new HashMap<>();

        while (parameters.hasMoreElements()) {

            String parameter = (String) parameters.nextElement();

            String value = request.getParameter(parameter);

            params.put(parameter, value);

        }

        return params;

    }

    /**

     * 获取请求Body

     *

     * @param request

     * @return

     */

    public static String getBodyString(ServletRequest request) {

        StringBuilder sb = new StringBuilder();

        InputStream inputStream = null;

        BufferedReader reader = null;

        try {

            inputStream = request.getInputStream();

            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            String line = "";

            while ((line = reader.readLine()) != null) {

                sb.append(line);

            }

        } catch (IOException e) {

            e.printStackTrace();

        } finally {

            if (inputStream != null) {

                try {

                    inputStream.close();

                } catch (IOException e) {

                    e.printStackTrace();

                }

            }

            if (reader != null) {

                try {

                    reader.close();

                } catch (IOException e) {

                    e.printStackTrace();

                }

            }

        }

        return sb.toString();

    }

}
