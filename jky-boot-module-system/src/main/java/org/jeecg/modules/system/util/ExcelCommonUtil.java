package org.jeecg.modules.system.util;

import org.apache.poi.ss.usermodel.Cell;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;

public class ExcelCommonUtil {

    public static String getCellValue(Cell cell) {
        String value = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        switch (cell.getCellType().name()) {
            case "STRING":
                value = cell.getRichStringCellValue().getString();
                break;
            case "NUMERIC":
                if ("General".equals(cell.getCellStyle().getDataFormatString())) {
                    BigDecimal bigDecimal = new BigDecimal(Double.valueOf(cell.getNumericCellValue()));
                    value = String.valueOf(bigDecimal);
                } else if ("m/d/yy".equals(cell.getCellStyle().getDataFormatString())) {
                    value = sdf.format(cell.getDateCellValue());
                } else {
                    value = String.valueOf(cell.getNumericCellValue());
                }
                break;
            case "BOOLEAN":
                value = String.valueOf(cell.getBooleanCellValue());
                break;
            case "BLANK":
                value = "";
                break;
            default:
                value = cell.toString();
                break;
        }
        return value;
    }

}
