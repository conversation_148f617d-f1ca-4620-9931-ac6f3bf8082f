package org.jeecg.modules.api.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.api.entity.gddoc.*;
import org.jeecg.modules.api.utils.RsaCommonUtil;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @project jky-gddoc-boot
 * @description 对外api
 * @date 2023/12/6
 */
@RestController
@RequestMapping("/out/api")
public class OutApiController {

    @Value("${outapi.gddoc.baseurl}")
    private String baseUrl;

    @Value("${outapi.gddoc.apikey}")
    private String apiKey;

    @Resource
    private ISysUserService sysUserService;

    /**
     * 获取项目、项目单体、企业接口概况：
     * 1.获取项目信息 /getProjectCollect
     * 2.获取项目关联单位信息 /getProjectEntCollect
     * 3.获取企业信息 /getEntCollect
     * 4.获取项目单体信息 /getMonomerCollect
     * 5.获取项目单体的关联企业信息 /getMonomerEntCollect
     * 6.根据企业获取项目信息 /getProjectByEnt
     *  7.获取单体的施工许可证信息 /getConsPermitCollect
     **/


    /**
     * @param pdProjectCollect
     * @description 获取项目信息
     * 传参说明：
     * 1.必传：projectName，模糊匹配；
     * 2.选传：projectId，projectTownId，dataType，isMajorProject
     * (字段说明和返回属性，请查看 PdProjectCollect.class 和《表结构信息-拟接口用-初稿-20231204》)
     * 3.只能查询最多返回20条数据
     * @date 2023/12/6
     * 传参demo
     * {
     * "projectName":"万科",
     * }
     * <p>
     * * getProjectCollect接口返回demo
     * * {
     * *     "success": true,
     * *     "message": "请求成功",
     * *     "code": 200,
     * *     "result": {
     * *         "total": 50,
     * *         "list": [
     * *             {
     * *                 "xmztz": 53000,
     * *                 "dataType": "6",
     * *                 "zjzmj": 185263,
     * *                 "projectState": "4",
     * *                 "projectTown": "寮步镇",
     * *                 "projectTownId": "52",
     * *                 "isMjorProject": "0",
     * *                 "projectAddress": "东莞市寮步镇横坑村",
     * *                 "jsxz": "新建",
     * *                 "projectName": "万科·城市高尔夫花园",
     * *                 "projectId": "0521001"
     * *             }
     * *           ]
     * *     },
     * *     "timestamp": 1702007486512
     * * }
     **/

    @PostMapping("/getProjectCollect")
    public Result getProjectCollect(@RequestBody PdProjectCollect pdProjectCollect) {
        if (ObjectUtil.isNotEmpty(pdProjectCollect)) {
            String url = baseUrl + "pcs/get-project-info";
            return getJkyData(url, pdProjectCollect);
        } else {
            return Result.error("参数错误，请检查！");
        }
    }

    /**
     * @param pdProjectEntCollect
     * @description 获取项目关联单位信息
     * 传参说明：
     * 1.必传：entType,projectId；
     * 2.选传：entName,entId
     * (字段说明和返回属性，请查看 PdProjectEntCollect.class 和《表结构信息-拟接口用-初稿-20231204》)
     * 3.只能查询最多返回20条数据
     * @date 2023/12/6
     * 传参demo
     * {
     * "projectId": "8a1145ea5afa1fa2015b56cfaa2401bc",
     * "entName": "",
     * "entId": "",
     * "entType": "2"
     * }
     * <p>
     * 返回demo:
     * {
     * "success": true,
     * "message": "请求成功",
     * "code": 200,
     * "result": {
     * "total": 1,
     * "list": [
     * {
     * "proLeader": "胡新立",
     * "entName": "中国市政工程中南设计研究总院有限公司",
     * "entId": "222",
     * "entLegalMobile": "13905427959",
     * "dataState": "1",
     * "entType": "2",
     * "creditCode": "91420100177666879T",
     * "proLeaderIdcard": "******************",
     * "entLegal": "李伟国",
     * "projectId": "8a1145ea5afa1fa2015b56cfaa2401bc",
     * "proLeaderMobile": "0769-88990999"
     * }
     * ]
     * },
     * "timestamp": 1702008611207
     * }
     */
    @PostMapping("/getProjectEntCollect")
    public Result getProjectEntCollect(@RequestBody PdProjectEntCollect pdProjectEntCollect) {
        if (ObjectUtil.isNotEmpty(pdProjectEntCollect) &&
                ObjectUtil.isNotEmpty(pdProjectEntCollect.getEntType()) &&
                ObjectUtil.isNotEmpty(pdProjectEntCollect.getProjectId())) {
            String url = baseUrl + "pcs/get-project-ent-info";
            return getJkyData(url, pdProjectEntCollect);
        } else {
            return Result.error("参数错误，请检查！");
        }
    }

    /**
     * @param pdEntInfoCollect
     * @description 获取企业信息
     * 传参说明：
     * 1.必传：entType，全匹配；
     * 2.选传：entAddress，corporationRegCode,entName,entId
     * (字段说明和返回属性，请查看 PdEntInfoCollect.class 和《表结构信息-拟接口用-初稿-20231204》)
     * 3.只能查询最多返回20条数据
     * @date 2023/12/6
     * 传参demo
     * {
     * "entName":"中邦生态环境",
     * "entType":"11"
     * }
     * <p>
     * 返回demo:
     * {
     * "success": true,
     * "message": "请求成功",
     * "code": 200,
     * "result": {
     * "total": 1,
     * "list": [
     * {
     * "entName": "中邦生态环境有限公司",
     * "entId": "8a1383cd784a9ffd01786e5243b9618b",
     * "entPhone": "043189689737",
     * "entAddress": "长春市高新区硅谷大街3355号超达创业园13幢612号房 ",
     * "corporationRegCode": "91220101675647443K",
     * "entType": "11"
     * }
     * ]
     * },
     * "timestamp": 1702007981401
     * }
     */
    @PostMapping("/getEntCollect")
    public Result getEntCollect(@RequestBody PdEntInfoCollect pdEntInfoCollect) {
        if (ObjectUtil.isNotEmpty(pdEntInfoCollect)) {
            String url = baseUrl + "pcs/get-ent-info";
            return getJkyData(url, pdEntInfoCollect);
        } else {
            return Result.error("参数错误，请检查！");
        }
    }

    /**
     * @param pdMonomerCollect
     * @description 获取项目单体信息
     * 传参说明：
     * 1.必传：projectId，全匹配；
     * 2.选传：monomerName，monomerId，licencemoney
     * (字段说明和返回属性，请查看 PdMonomerCollect.class 和《表结构信息-拟接口用-初稿-20231204》)
     * 3.只能查询最多返回20条数据
     * @date 2023/12/6
     * 传参demo
     * {
     * "projectId":"8a1145ea4f5e0335014f5ec06b3c0069"
     * }
     * <p>
     * 返回demo:
     * {
     * "success": true,
     * "message": "请求成功",
     * "code": 200,
     * "result": {
     * "total": 13,
     * "list": [
     * {
     * "gclb": "1",
     * "gczt": "4",
     * "monomerName": "帝庭山花园二期-60号住宅楼（框架4层1幢）",
     * "licencemoney": "441900201603281901",
     * "licencedate": "2016-03-28",
     * "projectId": "8a1145ea4f5e0335014f5ec06b3c0069",
     * "monomerId": "44df970630a8a6f3c784f40504272349"
     * }
     * ]
     * },
     * "timestamp": 1702008430870
     * }
     */
    @PostMapping("/getMonomerCollect")
    public Result getMonomerCollect(@RequestBody PdMonomerCollect pdMonomerCollect) {
        if (ObjectUtil.isNotEmpty(pdMonomerCollect) && ObjectUtil.isNotEmpty(pdMonomerCollect.getProjectId())) {
            String url = baseUrl + "pcs/get-monomer-info";
            return getJkyData(url, pdMonomerCollect);
        } else {
            return Result.error("参数错误，请检查！");
        }
    }

    /**
     * @param pdMonomerEntCollect
     * @description 获取项目单体的关联企业信息
     * 传参说明：
     * 1.必传：projectId 、monomerId 和  entType，全匹配；
     * 2.选传：monomerName
     * (字段说明和返回属性，请查看 PdMonomerEntCollect.class 和《表结构信息-拟接口用-初稿-20231204》)
     * 3.只能查询最多返回20条数据
     * @date 2023/12/6
     * 传参demo
     * {
     * "projectId":"8a1383cd7d8dfbb5017db1e47a9a0f6d",
     * "entType":"1",
     * "monomerId": "8a1383cd7f7bdf5e017fb59b41570c55"
     * }
     * <p>
     * 返回demo:
     * {
     * "success": true,
     * "message": "请求成功",
     * "code": 200,
     * "result": {
     * "total": 1,
     * "list": [
     * {
     * "entName": "深圳市岩土综合勘察设计有限公司",
     * "entId": "2561",
     * "entLegalMobile": "13828846595",
     * "entType": "1",
     * "creditCode": "91440300192482699N",
     * "entLegal": "莫志恒",
     * "projectId": "8a1383cd7d8dfbb5017db1e47a9a0f6d",
     * "monomerId": "8a1383cd7f7bdf5e017fb59b41570c55"
     * }
     * ]
     * },
     * "timestamp": 1702008483055
     * }
     */
    @PostMapping("/getMonomerEntCollect")
    public Result getMonomerEntCollect(@RequestBody PdMonomerEntCollect pdMonomerEntCollect) {
        if (ObjectUtil.isNotEmpty(pdMonomerEntCollect) &&
                ObjectUtil.isNotEmpty(pdMonomerEntCollect.getProjectId()) &&
                ObjectUtil.isNotEmpty(pdMonomerEntCollect.getMonomerId()) &&
                ObjectUtil.isNotEmpty(pdMonomerEntCollect.getEntType())) {
            String url = baseUrl + "pcs/get-monomer-ent-info";
            return getJkyData(url, pdMonomerEntCollect);
        } else {
            return Result.error("参数错误，请检查！");
        }
    }

    /**
     * @param pdProjectByEnt
     * @description 根据企业获取项目信息
     * 传参说明：
     * 1.必传：entId,entType，全匹配；
     * 2.选传：projectId，entName
     * (字段说明和返回属性，请查看 PdProjectByEnt.class)
     * 3.只能查询最多返回20条数据
     * @date 2023/12/7
     * 传参demo
     * {
     * "projectId":"8a1288e98b9377dc018ba95d551a0562",
     * "entName":"",
     * "entId":"8c42989ffeef43018d82990a5cb15bbc",
     * "entType":"1"
     * }
     * 返回demo:
     * {
     * "success": true,
     * "message": "请求成功",
     * "code": 200,
     * "result": {
     * "total": 1,
     * "list": [
     * {
     * "entName": "建材广州工程勘测院有限公司",
     * "entId": "8c42989ffeef43018d82990a5cb15bbc",
     * "xmztz": 60000,
     * "dataType": "7",
     * "zjzmj": 170275.71,
     * "jslb": "基建",
     * "jsgmjnr": "项目拟建设皇宫高新科技产业园，包含3栋生产厂房、1栋宿舍、1间配电房、1间垃圾站及地下空间等。项目占地面积为50081.09平方米，地上建筑物面积约为170275.71平方米 ，不计容的地下空间面积约为12766.36平方米。项目定位为智慧物联创新基地，拟打造成以轻型生产、研发设计、中试平台为功能载体的创新型高质量发展创新示范基地，预计年产值5亿元，年税收贡献不低于40万/亩。",
     * "projectState": "1",
     * "entType": "1",
     * "projectTown": "东城街道",
     * "projectTownId": "72",
     * "jhkgrq": "2023-10-01",
     * "jhjgrq": "2026-10-01",
     * "isMjorProject": "1",
     * "projectAddress": "东莞市东城街道山湖路东城段20号",
     * "jsxz": "新建",
     * "tdmj": 50081.09,
     * "projectName": "东莞市东城牛山社区“皇宫照明工改工项目”",
     * "projectId": "8a1288e98b9377dc018ba95d551a0562",
     * "tdyt": "工业用地"
     * }
     * ]
     * },
     * "timestamp": 1702009332057
     * }
     */
    @PostMapping("/getProjectByEnt")
    public Result getProjectByEnt(@RequestBody PdProjectByEnt pdProjectByEnt) {
        if (ObjectUtil.isNotEmpty(pdProjectByEnt) &&
                ObjectUtil.isNotEmpty(pdProjectByEnt.getEntId()) && ObjectUtil.isNotEmpty(pdProjectByEnt.getEntType())) {
            String url = baseUrl + "pcs/get-project-by-ent";
            return getJkyData(url, pdProjectByEnt);
        } else {
            return Result.error("参数错误，请检查！");
        }
    }

    //    7.获取单体的施工许可证信息 /getConsPermitCollect
    /**
     * @param pdConsPermitCollect
     * @description 获取单体的施工许可证信息
     * 传参说明：
     * 1.必传：无
     * 2.选传：dataId (单体表PdMonomerCollect的dataId)
     * (字段说明和返回属性，请查看 PdConsPermitCollect.class)
     * 3.只能查询最多返回20条数据
     * @date 2024/1/18
     * 传参demo
     * {
     * "consPermitId":"8a1288e98b9377dc018ba95d551a0562",
     * }
     * 返回demo:
     * {
     * "success": true,
     * "message": "请求成功",
     * "code": 200,
     * "result": {
     * "total": 1,
     * "list": [
     * {
     *                 "consPermitId": "1000",
     *                 "consPermitName": "厂房/宿舍(三至五层)",
     *                 "isFinishBackup": "0",
     *                 "startdate": "1991-01-01",
     *                 "enddate": "1991-11-01",
     *                 "licencedate": "1995-03-09",
     *                 "licencemoney": "1000",
     *                 "townshipName": "塘厦镇",
     *                 "consFileUrl": "https://zjj.dg.gov.cn/mware_cms/zzpw/sgxk/1000/viewLicense.action"
     *             }
     *     ]
     */
    @PostMapping("/getConsPermitCollect")
    public Result getConsPermitCollect(@RequestBody PdConsPermitCollect pdConsPermitCollect) {
        String url = baseUrl + "pcs/get-cons-permit-collect";
        return getJkyData(url, pdConsPermitCollect);
    }

    /**
     * 添加用户
     *
     * @param user 用户角色资料  必传字段{realname:"建科院(企业名称)" ,"organizationId":"911113213111（组织机构ID）","entType":"1（企业类型）"}
     * @return 返回结果
     * {
     * code: 0
     * message: "success"  message值：1、success 2、新增用户必要参数缺少 3、新增用户异常
     * result: null
     * success: true success 值：1、true（添加成功） 2、false（添加失败）
     * timestamp: 1703664882226 （当前时间戳）
     * }
     * @throws Exception
     */
    @PostMapping("/addDocEnt")
    public Result getProjectByEnt(@RequestBody SysUser user) {
        try {
            sysUserService.add(user);
            return Result.OK("添加成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("添加失败！");
        }
    }

    protected Result getJkyData(String url, Object paramObj) {
        JSONObject jsonObject = (JSONObject) JSONUtil.parse(paramObj);
        System.out.println("----------------------------------------");
        System.out.println("请求url==============>" + url);
        HttpResponse response = HttpRequest.post(url)
                .header("apiKey", apiKey)
                .body(jsonObject.toString())
                .execute();
        String res = response.body();
        System.out.println("响应结果=" + res);
        System.out.println("----------------------------------------");
        JSONObject resObj = JSONUtil.parseObj(res);
        if (ObjectUtil.isNotEmpty(resObj) && 200 == Integer.parseInt(resObj.getStr("status")) && "success".equals(resObj.getStr("msg"))) {
            String result = RsaCommonUtil.RSADecryptBase64ByPrivateKey(resObj.getStr("body"), RsaCommonUtil.privateKeyForGddoc);
            JSONObject data = JSONUtil.parseObj(result);
            String listStr = data.getStr("list");
            Map<String, Object> map = new HashMap<>();
            map.put("total", data.get("total"));
            map.put("list", JSONUtil.parseArray(listStr));
            return Result.OK("请求成功", map);
        }
        return Result.error("查询失败，请稍候重试！");
    }
}
