package org.jeecg.modules.api.entity.gddoc;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * 项目参建企业对象 pd_project_ent_collect
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
public class PdProjectEntCollect  {

    private static final long serialVersionUID=1L;

    /**
     * 数据主键
     */
    private String id;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 企业类型
     */
    private String entType;
    /**
     * 企业id
     */
    private String entId;
    /**
     * 企业名称
     */
    private String entName;
    /**
     * 企业统一社会信用代码
     */
    private String creditCode;
    /**
     * 企业资质等级
     */
    private String aptitudeGrade;
    /**
     * 企业资质证书编号
     */
    private String aptitudeCerNo;
    /**
     * 项目负责人
     */
    private String proLeader;
    /**
     * 项目负责人证件号
     */
    private String proLeaderIdcard;
    /**
     * 负责人证书编号
     */
    private String proLeaderCerNo;
    /**
     * 负责人联系电话
     */
    private String proLeaderMobile;
    /**
     * 负责人注册执业资格证号
     */
    private String proLeaderRegNo;
    /**
     * 负责人安全生产考核合格证号
     */
    private String proLeaderSafeNo;
    /**
     * 企业法定代表人
     */
    private String entLegal;
    /**
     * 企业法定代表人联系电话
     */
    private String entLegalMobile;
    /**
     * 数据状态, 0：无效、1：有效
     */
    private String dataState;
    /**
     *
     */
    private Date jkSyncTime;

    /**
     * 创建者
     */
    @TableField(exist = false)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(exist = false)
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Date createTime;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Date updateTime;

}
