package org.jeecg.modules.api.entity.gddoc;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 施工许可基本信息对象 pd_cons_permit_collect
 *
 * <AUTHOR>
 * @date 2024-01-16
 */
@Data
public class PdConsPermitCollect {

    private static final long serialVersionUID=1L;

    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 工程ID
     */
//    @TableId(value = "cons_permit_id")
    private String consPermitId;
    /**
     * 工程名称
     */
    private String consPermitName;
    /**
     * 安监项目监督号
     */
    private String ajProjectNum;
    /**
     * 质监项目监督号
     */
    private String zjProjectNum;
    /**
     * 安全监督登记号
     */
    private String ajNum;
    /**
     * 质量监督登记号
     */
    private String zjNum;
    /**
     * 是否竣工验收备案
     */
    private String isFinishBackup;
    /**
     * 竣工验收备案证书号
     */
    private String finishBackupNo;
    /**
     * 竣工验收备案日期
     */
    private Date finishBackupDate;
    /**
     * 是否终止监督
     */
    private String isStope;
    /**
     * 终止监督日期
     */
    private Date stopeDate;
    /**
     * 工程地点
     */
    private String projectaddress;
    /**
     * 建设规模
     */
    private Long constructsize;
    /**
     * 合同价格
     */
    private Long projecprice;
    /**
     * 最大跨度
     */
    private Long width;
    /**
     * 结构体系
     */
    private String frame;
    /**
     * 地上
     */
    private String abovelayer;
    /**
     * 地下
     */
    private String underlayer;
    /**
     * 计划开工时间
     */
    private Date startdate;
    /**
     * 计划竣工时间
     */
    private Date enddate;
    /**
     * 基坑深度
     */
    private Long holeheigh;
    /**
     * 基坑面积
     */
    private Long holearea;
    /**
     * 支护形式
     */
    private String timber;
    /**
     * 挖孔桩
     */
    private Long hpegheigh;
    /**
     * 地下暗挖
     */
    private Long ungrdheigh;
    /**
     * 塔吊
     */
    private Long towernum;
    /**
     * 高支模
     */
    private Long modheigh;
    /**
     * 外用电梯
     */
    private Long liftnum;
    /**
     * 发包范围_其中专业内容_施工总承包
     */
    private String isAllcontract;
    /**
     * 发包范围_其中专业内容_专业承包
     */
    private String isSpecialcontract;
    /**
     * 主体工程
     */
    private String isMainproject;
    /**
     * 地基与基础
     */
    private String isToft;
    /**
     * 钢结构
     */
    private String isSteelstructure;
    /**
     * 建筑防水
     */
    private String isKeepwater;
    /**
     * 电梯安装
     */
    private String isLifthit;
    /**
     * 幕墙
     */
    private String isScreenwall;
    /**
     * 金属门窗
     */
    private String isDoorwindows;
    /**
     * 管道
     */
    private String isPipeline;
    /**
     * 消防
     */
    private String isFirecontrol;
    /**
     * 给排水
     */
    private String isDevicehit;
    /**
     * 电气
     */
    private String isTelecom;
    /**
     * 通风空调
     */
    private String isElet;
    /**
     * 装饰装修
     */
    private String isSendelet;
    /**
     * 智能化
     */
    private String isAptutide;
    /**
     * 高耸构筑物
     */
    private String isHigharchi;
    /**
     * 园林古建筑
     */
    private String isParkarchi;
    /**
     * 其他
     */
    private String isOther;
    /**
     * 其他描述
     */
    private String othercontract;
    /**
     * 市政公用专业内容_施工总承包
     */
    private String isAllcontract2;
    /**
     * 市政公用专业内容_专业承包
     */
    private String isSpecialcontract2;
    /**
     * 土石方
     */
    private String isStone;
    /**
     * 桥梁
     */
    private String isBridge;
    /**
     * 隧道
     */
    private String isTube;
    /**
     * 城市交通安全设施
     */
    private String isCitysafe;
    /**
     * 城市交通通信、监控、收费综合系统
     */
    private String isTraffic;
    /**
     * 城市轨道交通
     */
    private String isCitytraffic;
    /**
     * 城市及道路照明
     */
    private String isCitylight;
    /**
     * 电信
     */
    private String isCivisicmtel;
    /**
     * 管道2
     */
    private String isCivisicmpipe;
    /**
     * 附属配套房建
     */
    private String isEncircle;
    /**
     * 城市及道路照明_其他
     */
    private String isCivisicmother;
    /**
     * 城市及道路照明_其他描述
     */
    private String othercivisicm;
    /**
     * 基础设施和公用事业工程建设项目
     */
    private String projectkind;
    /**
     * 基础设施_其他
     */
    private String otherprokin;
    /**
     * 国家融资
     */
    private Long autostate;
    /**
     * 国有
     */
    private Long statehold;
    /**
     * 政府
     */
    private Long autogov;
    /**
     * 集体
     */
    private Long autocollectivity;
    /**
     * 村集体经济组织
     */
    private Long villageorg;
    /**
     * 私营
     */
    private Long autoowned;
    /**
     * 外资
     */
    private Long outcapital;
    /**
     * 其他资本
     */
    private Long othercapital;
    /**
     * 签名日期
     */
    private Date signdate;
    /**
     * 法定代表人（签名）
     */
    private String legaldelegate;
    /**
     * 房地产项目
     */
    private String isHouseproject;
    /**
     * 房地产备案号
     */
    private String housebackupid;
    /**
     * 是否企业备案
     */
    private String isEnterpriseback;
    /**
     * 项目隶属关系
     */
    private String enterprisebackid;
    /**
     * 招标投标工程
     */
    private String isBid;
    /**
     * 户内装修(精装修)
     */
    private String checkrecordId;
    /**
     * 外部企业ID
     */
    private String outsideEnterpriseid;
    /**
     * 发包范围
     */
    private String applyType;
    /**
     * 超限高层建筑项目
     */
    private String enterpriseId;
    /**
     * 监控类型
     */
    private String monitortype;
    /**
     * 视频监控
     */
    private String video;
    /**
     * 视频监控点数量
     */
    private String videoNumber;
    /**
     * 重点工程
     */
    private String isImportant;
    /**
     * 工程性质
     */
    private String projectcharacter;
    /**
     * 重点工程（施工报建或施工许可）
     */
    private String stress;
    /**
     * 是否是镇街事项
     */
    private String twonstreet;
    /**
     * 统一社会信用代码或组织机构代码
     */
    private String creditCode;
    /**
     * 项目登记信息表id
     */
    private String proId;
    /**
     * 是否特殊建设工程
     */
    private String isSpecialLib;
    /**
     * 项目类型
     */
    private String projectType;
    /**
     * 住宅（产业转型升级基地项目）建筑面积
     */
    private Long houseArea;
    /**
     * 住宅（产业转型升级基地项目）套(个)数
     */
    private Long houseNum;
    /**
     * 商业（新型产业用地（m0））建筑面积
     */
    private Long storeArea;
    /**
     * 商业（新型产业用地（m0））套(个)数
     */
    private Long storeNum;
    /**
     * 办公建筑面积
     */
    private Long officeArea;
    /**
     * 办公套(个)数
     */
    private Long officeNum;
    /**
     * 其他建筑面积
     */
    private Long otherArea;
    /**
     * 其他套(个)数
     */
    private Long otherNum;
    /**
     * 其他用途
     */
    private String otherUse;
    /**
     * 省施工图审查合格书id
     */
    private String reviewId;
    /**
     * 省施工图审查合格书编号
     */
    private String reviewNo;
    /**
     * 省施工图审查合格书附件
     */
    private String reviewUrl;
    /**
     * 建筑高度
     */
    private BigDecimal height;
    /**
     * 建设单位类型
     */
    private String buildIsOrg;
    /**
     * 施工许可日期
     */
    private Date licencedate;
    /**
     * 许可证号
     */
    private String licencemoney;
    /**
     * 镇区id
     */
    private String townshipId;
    /**
     * 镇区名称
     */
    private String townshipName;
    /**
     * 工程类型
     */
    private String projecttypeId;
    /**
     * 投资类型
     */
    private String investtypeId;
    /**
     * 备注
     */
    private String adminssionremark;
    /**
     * 是否撤销
     */
    private String iscancel;
    /**
     * 许可内容
     */
    private String licenseContent;
    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 数据集成更新时间
     */
    private Date collectUpdateTime;
    /**
     * 建设工程规划许可证证号
     */
    private String gcghxkNo;
    /**
     * 建设规模（单位）
     */
    private String scaleType;
    /**
     *
     */
    private Date jkSyncTime;
    /**
     * 施工许可证 url
     */
    private String consFileUrl;;
}
