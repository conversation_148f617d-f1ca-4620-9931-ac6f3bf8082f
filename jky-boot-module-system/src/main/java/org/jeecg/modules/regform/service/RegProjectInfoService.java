package org.jeecg.modules.regform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.query.ProjectQuery;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.regform.domain.RegProjectInfo;
import org.jeecg.modules.system.entity.SysAnnouncementSend;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface RegProjectInfoService  extends IService<RegProjectInfo> {

    /**
     * 项目申报上传附件
     * @param dataId
     * @param request
     * @return
     */
    public String upload(String dataId, HttpServletRequest request);

    /**
     * 下载文件
     * @param fileId
     * @param response
     * @return
     */
    public void download(String fileId, HttpServletResponse response);

    public Boolean upload(MultipartFile[] files, String bizPath, String dataId);

    Result<Rs<PdProjectCollect>> queryProject(ProjectQuery query);

    /**
     * 查询项目全部字段
     * @param query
     * @return
     */
    Result<Rs<PdProjectCollect>> queryProjectAll(ProjectQuery query);
}
