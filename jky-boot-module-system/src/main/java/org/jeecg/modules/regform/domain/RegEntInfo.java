package org.jeecg.modules.regform.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("reg_ent_info")
public class RegEntInfo  implements Serializable {
    private static final long serialVersionUID=1L;

    @TableField(exist = false)
    private String key;

    /**
     * 数据主键
     */
    @TableId
    private String id;
    /**
     * 企业ID
     */
    private String entId;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 企业名称
     */
    private String entName;

    /**
     * 企业名称
     */
    private String entAddress;

    /**
     * 联系信息_联系电话
     */
    private String entPhone;

    /**
     * 工商注册所在地_省
     */
    private String regProvince;
    /**
     * 工商注册所在地_市
     */
    private String regCity;
    /**
     * 工商注册信息_营业执照注册号
     */
    private String licenceNo;
    /**
     * 工商注册信息_营业执照发证机关
     */
    private String licenceIssueDept;
    /**
     * 统一社会信用代码
     */
    private String corporationRegCode;
    /**
     * 统一社会信用代码发证机关
     */
    private String corporationRegDept;

    /**
     * 办公地址所在镇
     */
    private String setOfficeTown;
    /**
     * 办公详细地址
     */
    private String setOfficeaddress;

    /**
     * 企业类型
     */
    private String entType;

    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 信用档案状态
     */
    private String canBid;
    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 企业曾用名
     */
    private String formerName;

    /**
     * 创建者
     */

    private String createBy;

    /**
     * 更新者
     */

    private String updateBy;

    /**
     * 创建时间
     */

    private Date createTime;

    private Date updateTime;

    /**
     * 人员姓名
     */

    private String personName;

    /**
     * 人员身份证
     */

    private String personIdcard;

    /**
     * 人员电话
     */

    private String personTel;

    /**
     * 人员岗位
     */

    private String personPost;

    /**
     * 人员头像
     */

    private String personHead;

}
