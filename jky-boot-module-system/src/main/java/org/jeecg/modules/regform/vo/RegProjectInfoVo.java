package org.jeecg.modules.regform.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.regform.domain.RegEntInfo;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class RegProjectInfoVo implements Serializable {

    private String id;

    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 项目编号/项目代码
     */
    private String projectNo;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目所在镇（街）ID
     */
    private String projectTownId;
    /**
     * 项目所在镇（街）
     */
    private String projectTown;
    /**
     * 项目详细地址
     */
    private String projectAddress;
    /**
     * 建设单位ID
     */
    private String consUnitId;
    /**
     * 项目类型
     */
    private String dataType;
    /**
     * 是否装配式建筑
     */
    private String isBuild;
    /**
     * 宗地代码
     */
    private String landNo;
    /**
     * 行政区
     */
    private String xzq;
    /**
     * 项目坐落
     */
    private String xmzl;
    /**
     * 项目总投资（万元）
     */
    private Long xmztz;
    /**
     * 建设单位
     */
    private String consUnit;
    /**
     * 土地面积（平方米）
     */
    private Long tdmj;
    /**
     * 总建筑面积（平方米）
     */
    private Long zjzmj;
    /**
     * 计划开工日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date jhkgrq;
    /**
     * 计划竣工日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date jhjgrq;
    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 土地用途
     */
    private String tdyt;
    /**
     * 建设性质
     */
    private String jsxz;
    /**
     * 建设类别
     */
    private String jslb;
    /**
     * 容积率
     */
    private Long rjl;
    /**
     * 绿地率
     */
    private Long ldl;
    /**
     * 建设规模及内容
     */
    private String jsgmjnr;
    /**
     * 项目状态
     */
    private String projectState;
    /**
     * 是否重大项目 1.是  0.否
     */
    private String isMajorProject;

    /**
     * 单位名称
     */
    private String decUnitName;

    /**
     * 单位组织机构代码
     */
    private String decUnitCode;

    /**
     * 单位地址
     */
    private String decUnitAddress;

    /**
     * 单位联系姓名
     */
    private String decUnitContacts;

    /**
     * 单位联系电话
     */
    private String decUnitTel;

    /**
     * 单位联系邮件
     */
    private String decUnitEmail;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private String updateBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 项目经理姓名
     */
    private String managerName;
    /**
     * 项目经理电话
     */
    private String managerTel;
    /**
     * 退回原因
     */
    private String returnBackReason;
    /**
     * 审核 状态，0可修改勤劳退回，2提交成功，3通过
     */
    private String status;

//    /**
//     * 企业社会信用代码(查询项目时过滤企业)
//     */
//    private String organizationId;

    /**
     * 管理人员信息
     */
    private List<RegEntInfo> regEntInfoList;

    /**
     * 验证码
     */
    private String captcha;
    /**
     * 验证码key
     */
    private String checkKey;

    /**
     * 上传路径
     */
    private String bizPath;

}
