package org.jeecg.modules.regform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgdoc.constants.UserRoleConstants;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.query.ProjectQuery;
import com.jky.dgdoc.fegin.DgdocApiClient;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.regform.domain.RegEntInfo;
import org.jeecg.modules.regform.domain.RegFile;
import org.jeecg.modules.regform.domain.RegProjectInfo;
import org.jeecg.modules.regform.mapper.RegEntInfoMapper;
import org.jeecg.modules.regform.mapper.RegFileMapper;
import org.jeecg.modules.regform.mapper.RegProjectInfoMapper;
import org.jeecg.modules.regform.service.RegEntInfoService;
import org.jeecg.modules.regform.service.RegProjectInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

/**
 * @Author: lpg
 * @Date: 2024/01/02/16:49
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class RegProjectnfoServiceImpl extends ServiceImpl<RegProjectInfoMapper, RegProjectInfo> implements RegProjectInfoService {

    private final DgdocApiClient dgDocApiClient;

    @Value(value = "${jky.path.regfromUpload}")
    private String regfromUploadPath;

    @Autowired(required = false)
    private RegFileMapper regFileMapper;

    private List<String> fileTypeList = Arrays.asList("docx,doc,pdf,jpg,jpeg,xlsx,xls,png".split(","));


    @Override
    @Transactional
    public String upload(String dataId, HttpServletRequest request) {
        String savePath = "";
        String bizPath = request.getParameter("biz");
        //LOWCOD-2580 sys/common/upload接口存在任意文件上传漏洞
        if (oConvertUtils.isNotEmpty(bizPath) && (bizPath.contains("../") || bizPath.contains("..\\"))) {
            throw new JeecgBootException("上传目录bizPath，格式非法！");
        }
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file =  multipartRequest.getFile("file");// 获取上传文件对象

        if (file == null) {
            return "";
        }
        String fileType = FilenameUtils.getExtension(file.getOriginalFilename());
        if (!fileTypeList.contains(fileType)) {
            throw new JeecgBootException("上传文件格式异常");
        }
        savePath = uploadLocal(file, bizPath);
        RegFile regFile = new RegFile();
        regFile.setUrl(savePath);
        regFile.setFileName(file.getOriginalFilename());
        regFile.setType(fileType);
        regFile.setDelFlag("0");
        regFile.setTableId(dataId);
        regFileMapper.insert(regFile);
        return file.getOriginalFilename();
    }

    @Override
    public void download(String fileId, HttpServletResponse response) {
        QueryWrapper<RegFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", fileId);
        RegFile regFile = regFileMapper.selectOne(queryWrapper);
        File file = new File(regfromUploadPath+regFile.getUrl());
        if (!file.exists()) {
            log.error("fileId= "+ fileId +" 文件不存在！");
            throw new RuntimeException("文件[" + fileId + "]不存在..");
        }
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition", "attachment;fileName=" + new String(file.getName().getBytes("UTF-8"), "iso-8859-1"));
            inputStream = new BufferedInputStream(new FileInputStream(regfromUploadPath + regFile.getUrl()));
            outputStream = response.getOutputStream();
            byte[] buf = new byte[1024];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (IOException e) {
            log.error("下载文件失败" + e.getMessage());
            response.setStatus(404);
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public Boolean upload(MultipartFile[] files, String bizPath, String dataId) {
        String savePath = "";
        //LOWCOD-2580 sys/common/upload接口存在任意文件上传漏洞
        if (oConvertUtils.isNotEmpty(bizPath) && (bizPath.contains("../") || bizPath.contains("..\\"))) {
            throw new JeecgBootException("上传目录bizPath，格式非法！");
        }
        for (MultipartFile file : files) {
            savePath = uploadLocal(file, bizPath);
            RegFile regFile = new RegFile();
            regFile.setUrl(savePath);
            regFile.setFileName(file.getOriginalFilename());
            regFile.setType(FilenameUtils.getExtension(file.getOriginalFilename()));
            regFile.setDelFlag("0");
            regFile.setTableId(dataId);
            regFileMapper.insert(regFile);
        }
        return true;
    }


    /**
     * 本地文件上传
     *
     * @param mf      文件
     * @param bizPath 自定义路径
     * @return
     */
    private String uploadLocal(MultipartFile mf, String bizPath) {
        try {
            String ctxPath = regfromUploadPath;
            String fileName = null;
            File file = new File(ctxPath + File.separator + bizPath + File.separator);
            if (!file.exists()) {
                file.mkdirs();// 创建文件根目录
            }
            String orgName = mf.getOriginalFilename();// 获取文件名
            orgName = CommonUtils.getFileName(orgName);
            if (orgName.indexOf(".") != -1) {
                fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.lastIndexOf("."));
            } else {
                fileName = orgName + "_" + System.currentTimeMillis();
            }
            String savePath = file.getPath() + File.separator + fileName;
            File savefile = new File(savePath);
            FileCopyUtils.copy(mf.getBytes(), savefile);
            String dbpath = null;
            if (oConvertUtils.isNotEmpty(bizPath)) {
                dbpath = bizPath + File.separator + fileName;
            } else {
                dbpath = fileName;
            }
            if (dbpath.contains("\\")) {
                dbpath = dbpath.replace("\\", "/");
            }
            return dbpath;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }
    @Override
    public Result<Rs<PdProjectCollect>> queryProject(ProjectQuery query) {
        PdProjectCollect projectCollect = new PdProjectCollect();
        projectCollect.setProjectName(query.getProjectName());
        projectCollect.setProjectId(query.getProjectId());
        Result<Rs<PdProjectCollect>> pdProjectCollect = dgDocApiClient.getProjectCollect(projectCollect);
        Rs<PdProjectCollect> result = pdProjectCollect.getResult();
        if (result != null) {
            List<PdProjectCollect> list = result.getList();
            List<PdProjectCollect> resultList = new ArrayList<>();
            for (int i = 0; i < 5; i++) {
                if (i>=list.size()) {
                    break;
                }
                PdProjectCollect pdProjectCollect1 = new PdProjectCollect();
                pdProjectCollect1.setProjectId(list.get(i).getProjectId());
                pdProjectCollect1.setProjectName(list.get(i).getProjectName());
                pdProjectCollect1.setProjectTown(list.get(i).getProjectTown());
                pdProjectCollect1.setProjectNo(list.get(i).getProjectNo());
                resultList.add(pdProjectCollect1);
            }
            result.setList(resultList);
        }
        return pdProjectCollect;
    }

    @Override
    public Result<Rs<PdProjectCollect>> queryProjectAll(ProjectQuery query) {
        PdProjectCollect projectCollect = new PdProjectCollect();
        projectCollect.setProjectName(query.getProjectName());
        projectCollect.setProjectId(query.getProjectId());
        Result<Rs<PdProjectCollect>> pdProjectCollect = dgDocApiClient.getProjectCollect(projectCollect);
        Rs<PdProjectCollect> result = pdProjectCollect.getResult();
        return pdProjectCollect;
    }
}
