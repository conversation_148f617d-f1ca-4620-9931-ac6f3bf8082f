package org.jeecg.modules.regform.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.PdProjectEntCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.query.ProjectEntQuery;
import com.jky.dgdoc.domain.query.ProjectQuery;
import com.jky.dgdoc.service.IDgdocProjectService;
import com.jky.modules.estar.tw.util.StringUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.MD5Util;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.api.controller.OutApiController;
import org.jeecg.modules.api.entity.gddoc.PdEntInfoCollect;
import org.jeecg.modules.regform.domain.RegEntInfo;
import org.jeecg.modules.regform.domain.RegFile;
import org.jeecg.modules.regform.domain.RegProjectInfo;
import org.jeecg.modules.regform.dto.RegProjectInfoDto;
import org.jeecg.modules.regform.service.RegEntInfoService;
import org.jeecg.modules.regform.service.RegFileService;
import org.jeecg.modules.regform.service.RegProjectInfoService;
import org.jeecg.modules.regform.vo.RegProjectInfoVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.IOException;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/regform")
public class RegformController {
    @Resource
    private RegEntInfoService regEntInfoService;
    @Resource
    private RegFileService regFileService;
    @Resource
    private RegProjectInfoService regProjectInfoService;

    @Autowired(required = false)
    private RedisUtil redisUtil;

    @Autowired(required = false)
    private RedisTemplate<String, String> redisTemplate;

    @Autowired(required = false)
    OutApiController outApiController;

    @Autowired(required = false)
    ISysBaseAPI sysBaseAPI;

    @Autowired(required = false)
    IDgdocProjectService dgdocProjectService;

    private Object countObject = new Object();

    @Value(value = "${jky.path.regfromUpload}")
    private String regfromUploadPath;

    @Value("${outapi.gddoc.baseurl}")
    private String baseUrl;

    @Transactional
    @RequestMapping("/outupdate")
    public Result<?> add(@RequestBody RegProjectInfoVo regProjectInfoVo) throws IOException {
        Result<Object> result = new Result<>();
        String captcha = regProjectInfoVo.getCaptcha();
        if (captcha == null) {
            result.error500("验证码无效");
            return result;
        }
        String lowerCaseCaptcha = captcha.toLowerCase();
        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha + regProjectInfoVo.getCheckKey(), "utf-8");
        Object checkCode = redisUtil.get(realKey);
        //当进入登录页时，有一定几率出现验证码错误 #1714
        if (checkCode == null || !checkCode.toString().equals(lowerCaseCaptcha)) {
            result.error500("验证码错误");
            return result;
        }
        RegProjectInfo regProjectInfo = new RegProjectInfo();
        BeanUtils.copyProperties(regProjectInfoVo, regProjectInfo);
        regProjectInfo.setStatus("2");
        regProjectInfoService.save(regProjectInfo);
        List<RegEntInfo> regEntInfoList = regProjectInfoVo.getRegEntInfoList();
        if(!CollectionUtils.isEmpty(regEntInfoList)) {
            for (RegEntInfo regEntInfo : regEntInfoList) {
                regEntInfo.setProjectId(regProjectInfo.getId());
                byte[] bytes = Base64.getDecoder().decode(regEntInfo.getPersonHead().replace("data:image/jpeg;base64,", ""));
                /**
                 * 上传头像
                 */
                String dirPath =  File.separator + "regfromStatic" + File.separator + regProjectInfo.getId() + File.separator + "icon";
                String fileName = regEntInfo.getPersonName() + "_" + System.currentTimeMillis() + ".jpg";
                File dir = new File(regfromUploadPath + dirPath);
                if (!dir.exists()) {
                    dir.mkdirs();// 创建文件
                }
                File file = new File(dir.getPath() + File.separator + fileName);
                FileUtils.writeByteArrayToFile(file,bytes);
                regEntInfo.setPersonHead(dirPath + File.separator + fileName);
            }
            regEntInfoService.saveBatch(regEntInfoList);
        }
        redisUtil.set(realKey + "Count", 0, 120);
        return Result.OK("添加成功", regProjectInfo);
    }

    @Transactional
    @PostMapping("/upload/{dataId}/{captcha}/{checkKey}")
    public Result<?> upload(@NotNull @PathVariable String dataId, @PathVariable String captcha, @PathVariable String checkKey, HttpServletRequest request) {
        if (StringUtils.isBlank(dataId)) {
            return Result.error("文件上传异常");
        }
        Result<Object> result = new Result<>();
        if (captcha == null) {
            result.error500("验证码无效");
            return result;
        }
        String lowerCaseCaptcha = captcha.toLowerCase();
        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha + checkKey, "utf-8");
        Object checkCode = redisUtil.get(realKey);
        //当进入登录页时，有一定几率出现验证码错误 #1714
        if (checkCode == null || !checkCode.toString().equals(lowerCaseCaptcha)) {
            result.error500("验证码错误");
            return result;
        }
        Integer count = null;
        synchronized (countObject) {
            count = (Integer) redisUtil.get(realKey + "Count");
            if (count == null) {
                result.error500("验证码无效");
                return result;
            }
            redisUtil.set(realKey + "Count", ++count);
        }
        if (count <= 10) {
            String fileName = regProjectInfoService.upload(dataId, request);
            return Result.OK(fileName+"上传成功");
        } else {
            redisUtil.del(realKey + "Count");
            return Result.error("文件上传数量不能超过10个");
        }
    }

    @Transactional
    @GetMapping("/download/{fileId}")
    public void download(@NotBlank(message = "文件ID不能为空") @PathVariable String fileId, HttpServletResponse response) {
        regProjectInfoService.download(fileId, response);
    }

    @GetMapping("/outquery")
    public Result<?> queryById(@RequestParam("id") String id) {
        RegProjectInfo regProjectInfo = regProjectInfoService.getById(id);
        QueryWrapper<RegEntInfo> regEntInfoQueryWrapper = new QueryWrapper<>();
        regEntInfoQueryWrapper.eq("project_id", regProjectInfo.getId());
        List<RegEntInfo> regEntInfoList = regEntInfoService.list(regEntInfoQueryWrapper);
        for (RegEntInfo regEntInfo : regEntInfoList) {
             // 可修改身份证
            if (!"0".equals(regProjectInfo.getStatus())){
                String idCard = regEntInfo.getPersonIdcard();
                String personTel = regEntInfo.getPersonTel();
                if (idCard.length() == 15){
                    idCard = idCard.replaceAll("(\\w{6})\\w*(\\w{3})", "$1******$2");
                }
                if (idCard.length() == 18){
                    idCard = idCard.replaceAll("(\\w{6})\\w*(\\w{3})", "$1*********$2");
                }
                personTel = personTel.replaceAll("(\\w{3})\\w*(\\w{4})", "$1*****$2");
                regEntInfo.setPersonIdcard(idCard);
                regEntInfo.setPersonTel(personTel);
            }
            regEntInfo.setKey(regEntInfo.getId());
        }
        QueryWrapper<RegFile> regFileQueryWrapper = new QueryWrapper<>();
        regFileQueryWrapper.eq("table_id", regProjectInfo.getId());
        regFileQueryWrapper.eq("del_flag","0");
        List<RegFile> regFileList = regFileService.list(regFileQueryWrapper);
        regProjectInfo.setRegEntInfoList(regEntInfoList);
        regProjectInfo.setRegFileList(regFileList);
        return Result.OK("查询成功！", regProjectInfo);
    }

    @GetMapping("/approveQueryById")
    public Result<?> approveQueryById(@RequestParam("id") String id) {
        RegProjectInfoDto regProjectInfoDto = new RegProjectInfoDto();
        RegProjectInfo regProjectInfo = regProjectInfoService.getById(id);
        QueryWrapper<RegEntInfo> regEntInfoQueryWrapper = new QueryWrapper<>();
        regEntInfoQueryWrapper.eq("project_id", regProjectInfo.getId());
        List<RegEntInfo> regEntInfoList = regEntInfoService.list(regEntInfoQueryWrapper);
        QueryWrapper<RegFile> regFileQueryWrapper = new QueryWrapper<>();
        regFileQueryWrapper.eq("table_id", regProjectInfo.getId());
        regFileQueryWrapper.eq("del_flag","0");
        List<RegFile> regFileList = regFileService.list(regFileQueryWrapper);
        regProjectInfo.setRegEntInfoList(regEntInfoList);
        regProjectInfo.setRegFileList(regFileList);
        BeanUtils.copyProperties(regProjectInfo,regProjectInfoDto);

        ProjectEntQuery projectEntQuery = new ProjectEntQuery();
        projectEntQuery.setProjectId(regProjectInfo.getProjectId());
        projectEntQuery.setEntType("18");
        Result<Rs<PdProjectEntCollect>> rsResult = dgdocProjectService.queryEntInfo(projectEntQuery);
        List<PdProjectEntCollect> list = rsResult.getResult().getList();
        if (!CollectionUtils.isEmpty(list)) {
            PdProjectEntCollect pdProjectEntCollect = list.get(0);
            regProjectInfoDto.setDecUnitAddress2("-");
            regProjectInfoDto.setDecUnitCode2(pdProjectEntCollect.getCreditCode());
            regProjectInfoDto.setDecUnitContacts2(pdProjectEntCollect.getProLeader());
            regProjectInfoDto.setDecUnitEmail2("-");
            regProjectInfoDto.setDecUnitName2(pdProjectEntCollect.getEntName());
            regProjectInfoDto.setDecUnitTel2(pdProjectEntCollect.getProLeaderMobile());
        }
        // 获取官方比对数据
        ProjectQuery projectQuery = new ProjectQuery();
        projectQuery.setProjectId(regProjectInfo.getProjectId());
        Result<Rs<PdProjectCollect>> queryProject = regProjectInfoService.queryProjectAll(projectQuery);
        if (queryProject.getResult() != null) {
            List<PdProjectCollect> projectCollectList = queryProject.getResult().getList();
            if (!CollectionUtils.isEmpty(projectCollectList)) {
                PdProjectCollect pdProjectCollect = projectCollectList.get(0);
                regProjectInfoDto.setProjectName2(pdProjectCollect.getProjectName());
                regProjectInfoDto.setProjectId2(pdProjectCollect.getProjectId());
                regProjectInfoDto.setProjectNo2(pdProjectCollect.getProjectNo());
                regProjectInfoDto.setProjectAddress2(pdProjectCollect.getProjectAddress());
                regProjectInfoDto.setProjectState2(pdProjectCollect.getProjectState());
                regProjectInfoDto.setProjectTown2(pdProjectCollect.getProjectTown());
                regProjectInfoDto.setManagerName2("-");
                regProjectInfoDto.setManagerTel2("-");
                regProjectInfoDto.setJhkgrq2(pdProjectCollect.getJhkgrq());
                regProjectInfoDto.setJhjgrq2(pdProjectCollect.getJhjgrq());
                regProjectInfoDto.setZjzmj2(pdProjectCollect.getZjzmj());
                regProjectInfoDto.setJsgmjnr2(pdProjectCollect.getJsgmjnr());
            }
        }
        return Result.OK("查询成功！", regProjectInfoDto);
    }

    @GetMapping("/outQueryByTel")
    public Result<?> outQueryByTel(RegProjectInfo regProjectInfoVo ,@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest request) {
        QueryWrapper<RegProjectInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("manager_tel",regProjectInfoVo.getManagerTel());
        queryWrapper.eq("manager_name",regProjectInfoVo.getManagerName());
        Page<RegProjectInfo> page = new Page<>(pageNo, pageSize);
        IPage<RegProjectInfo> list = regProjectInfoService.page(page,queryWrapper);
        return Result.OK("查询成功！", list);
    }

    @GetMapping("/list")
    public Result<?> list(RegProjectInfo regProjectInfo, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest request) {
        QueryWrapper<RegProjectInfo> queryWrapper = QueryGenerator.initQueryWrapper(regProjectInfo, request.getParameterMap());
        Page<RegProjectInfo> page = new Page<>(pageNo, pageSize);
        IPage<RegProjectInfo> pageList = regProjectInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @Transactional
    @PutMapping("/outEdit")
    public Result<?> edit(@RequestBody RegProjectInfoVo regProjectInfoVo) throws IOException {
        Result<Object> result = new Result<>();
        String captcha = regProjectInfoVo.getCaptcha();
        if (captcha == null) {
            result.error500("验证码无效");
            return result;
        }
        String lowerCaseCaptcha = captcha.toLowerCase();
        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha + regProjectInfoVo.getCheckKey(), "utf-8");
        Object checkCode = redisUtil.get(realKey);
        //当进入登录页时，有一定几率出现验证码错误 #1714
        if (checkCode == null || !checkCode.toString().equals(lowerCaseCaptcha)) {
            result.error500("验证码错误");
            return result;
        }
        RegProjectInfo regProjectInfo = new RegProjectInfo();
        BeanUtils.copyProperties(regProjectInfoVo, regProjectInfo);
        regProjectInfo.setStatus("2");
        regProjectInfoService.updateById(regProjectInfo);

        // 先删除企业管理人员资料
        QueryWrapper<RegEntInfo> regEntInfoQueryWrapper = new QueryWrapper<>();
        regEntInfoQueryWrapper.eq("project_id",regProjectInfo.getId());
        regEntInfoService.remove(regEntInfoQueryWrapper);
        List<RegEntInfo> regEntInfoList = regProjectInfoVo.getRegEntInfoList();
        for (RegEntInfo regEntInfo : regEntInfoList) {
            regEntInfo.setProjectId(regProjectInfo.getId());
            if (regEntInfo.getPersonHead().indexOf("data:image/jpeg;base64,") != -1) {
                byte[] bytes = bytes = Base64.getDecoder().decode(regEntInfo.getPersonHead().replace("data:image/jpeg;base64,", ""));
                /**
                 * 上传头像
                 */
                String dirPath =  File.separator + "regfromStatic" + File.separator + regProjectInfo.getId() + File.separator + "icon";
                String fileName = regEntInfo.getPersonName() + "_" + System.currentTimeMillis() + ".jpg";
                File dir = new File(regfromUploadPath + dirPath);
                if (!dir.exists()) {
                    dir.mkdirs();// 创建文件
                }
                File file = new File(dir.getPath() + File.separator + fileName);
                FileUtils.writeByteArrayToFile(file,bytes);
                regEntInfo.setPersonHead(dirPath + File.separator + fileName);
            }
        }
        regEntInfoService.saveBatch(regEntInfoList);
        redisUtil.set(realKey + "Count", 0, 120);
        return Result.OK("编辑成功", regProjectInfo);
    }

    @Transactional
    @PutMapping("/approve")
    public Result<RegProjectInfo> approve(@RequestBody RegProjectInfo regProjectInfo) {
        regProjectInfoService.updateById(regProjectInfo);
        return Result.OK("审批成功", regProjectInfo);
    }

    @Transactional
    @DeleteMapping("/del")
    public Result<?> del(@RequestParam String projectId) {
        regProjectInfoService.removeById(projectId);
        QueryWrapper<RegEntInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        regEntInfoService.remove(queryWrapper);
        UpdateWrapper<RegFile> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("project_id", projectId);
        updateWrapper.set("del_flag", "1");
        regFileService.update(new RegFile(), updateWrapper);
        return Result.OK("删除成功");
    }

    @Transactional
    @DeleteMapping("/delFile")
    public Result<?> delFile(@RequestParam String fileId,@RequestParam String captcha, @RequestParam String checkKey) {
        Result<Object> result = new Result<>();
        if (captcha == null) {
            result.error500("验证码无效");
            return result;
        }
        String lowerCaseCaptcha = captcha.toLowerCase();
        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha + checkKey, "utf-8");
        Object checkCode = redisUtil.get(realKey);
        //当进入登录页时，有一定几率出现验证码错误 #1714
        if (checkCode == null || !checkCode.toString().equals(lowerCaseCaptcha)) {
            result.error500("验证码错误");
            return result;
        }
        UpdateWrapper<RegFile> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", fileId);
        updateWrapper.set("del_flag", "1");
        regFileService.update(new RegFile(), updateWrapper);
        return Result.OK("删除成功");
    }

    /**
     * 根据项目名称查询项目信息
     */
    @ApiOperation("根据项目名称查询项目信息")
    @GetMapping("/outQueryProject")
    public Result<List<PdProjectCollect>> queryProject(@Validated ProjectQuery query) {
        Result<List<PdProjectCollect>> result = new Result<>();
        Result<Rs<PdProjectCollect>> rsResult = regProjectInfoService.queryProject(query);
        if (!rsResult.isSuccess()) {
            log.error(rsResult.getMessage());
            result.error500("网络异常，请联系管理员！");
            return result;
        }
        result.setResult(rsResult.getResult().getList());
        return result;
    }

    /**
     * 查询企业名称
     */
    @ApiOperation("查询企业名称")
    @PostMapping("/outQueryEntCollect")
    public Result<?> outQueryEntCollect(@RequestBody PdEntInfoCollect pdEntInfoCollect) {
        Result<Map<String,Object>> entCollect = outApiController.getEntCollect(pdEntInfoCollect);
//        Map<String, Object> map = entCollect.getResult();
//        JSONArray list = (JSONArray) map.get("list");
//        for (int i = 0; i < list.size(); i++) {
//            JSONObject ent = (JSONObject) list.get(i);
//            List<String> entType = sysBaseAPI.loadDictItem("dgzjjsp_ent_type", (String) ent.get("entType"));
//            ent.put("entType",entType);
//        }
        return entCollect;
    }
}
