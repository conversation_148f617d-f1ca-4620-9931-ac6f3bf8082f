<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
		<artifactId>jky-boot-parent</artifactId>
		<groupId>com.jky.boot</groupId>
		<version>3.1</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

    <artifactId>jky-boot-module-im</artifactId>
    <groupId>com.jky.modules.im</groupId>
    <version>1.0.0</version>
    
    <properties> <!-- 增加这部分，避免pom.xm文件报错 -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
		<skipTests>true</skipTests>
	</properties>

    <dependencies>
        <dependency>
            <groupId>com.jky.boot</groupId>
            <artifactId>jky-boot-base-core</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.t-io/tio-websocket-server -->
        <dependency>
            <groupId>org.t-io</groupId>
            <artifactId>tio-websocket-server</artifactId>
            <version>3.7.0.v20201010-RELEASE</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/redis.clients/jedis -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>4.3.0-m2</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.21</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.data/spring-data-redis -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>2.6.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-pool2 -->
    </dependencies>
</project>

