<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jky.boot</groupId>
        <artifactId>jky-boot-parent</artifactId>
        <version>3.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jky-boot-starter</artifactId>
    <packaging>pom</packaging>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <module>jky-boot-starter-job</module>
        <module>jky-boot-starter-lock</module>
        <module>jky-boot-starter-rabbitmq</module>
    </modules>
    <dependencies>
        <!--jky-tools-->
        <dependency>
            <groupId>com.jky.boot</groupId>
            <artifactId>jky-boot-base-tools</artifactId>
        </dependency>
        <!--加载配置信息-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>