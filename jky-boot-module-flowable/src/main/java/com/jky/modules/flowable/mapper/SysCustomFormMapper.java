package com.jky.modules.flowable.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.modules.flowable.domain.vo.CustomFormVo;
import com.jky.modules.flowable.entity.SysCustomForm;

/**
 * @Description: 系统自定义表单表
 * @Author: jky
 * @Date:   2022-04-23
 * @Version: V1.0
 */
public interface SysCustomFormMapper extends BaseMapper<SysCustomForm> {
	SysCustomForm selectSysCustomFormById(String formId);
	SysCustomForm selectSysCustomFormByServiceName(String serviceName);
	void updateCustom(@Param("customFormVo") CustomFormVo customFormVo);
}
