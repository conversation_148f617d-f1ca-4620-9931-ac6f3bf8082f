package com.jky.dgswr.handler;

import cn.hutool.core.lang.generator.Generator;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * @description:
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class IdGeneratorHelper {

    private static final Generator<Long> generator = new SnowflakeGenerator();

    public static String next() {
        return String.valueOf(generator.next());
    }

    public static void main(String[] args) {
        System.out.println(next());
    }
}

