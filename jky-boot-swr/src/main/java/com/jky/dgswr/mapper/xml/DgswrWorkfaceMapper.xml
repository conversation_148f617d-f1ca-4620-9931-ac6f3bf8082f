<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.dgswr.mapper.DgswrWorkfaceMapper">


    <insert id="insertGeometry">
        INSERT INTO dgswr_workface (workface_id, project_id, workface_name, address, lat, lon, geom, remark,
                                    introduction, details, is_reported, create_by, update_by)
        VALUES (#{e.workfaceId}, #{e.projectId}, #{e.workfaceName}, #{e.address}, #{e.lat}, #{e.lon},
                ST_GeomFromText(#{e.geom,typeHandler=com.jky.dgswr.handler.GeometryTypeHandler,jdbcType=BLOB}),
                #{e.remark}, #{e.introduction}, #{e.details}, #{e.isReported}, #{e.createBy}, #{e.updateBy})
    </insert>
</mapper>