package com.jky.dgswr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgswr.domain.DgswrProject;
import com.jky.dgswr.domain.DgswrReport;
import com.jky.dgswr.domain.DgswrWorkface;
import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.vo.DgswrProjectVo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.em.DgswrProjectStatus;
import com.jky.dgswr.mapper.DgswrProjectMapper;
import com.jky.dgswr.mapper.DgswrReportMapper;
import com.jky.dgswr.mapper.DgswrWorkfaceMapper;
import com.jky.dgswr.service.IDgswrReportService;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 上报内容管理Service实现类
 */
@Service
@RequiredArgsConstructor
public class DgswrReportServiceImpl extends ServiceImpl<DgswrReportMapper, DgswrReport> implements IDgswrReportService {

    private final DgswrProjectMapper dgswrProjectMapper;
    private final DgswrWorkfaceMapper dgswrWorkfaceMapper;

    private final IDgswrWorkfaceService dgswrWorkfaceService;

    @Override
    public DgswrReportVo queryById(String reportId) {
        DgswrReport report = baseMapper.selectById(reportId);
        if (report == null) {
            return null;
        }
        return convertToVo(report);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(DgswrReportBo bo) {
        DgswrReport report = new DgswrReport();
        BeanUtils.copyProperties(bo, report);

        report.setReportTime(new Date());
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotNull(loginUser)) {
            report.setReportUserId(loginUser.getUsername());
            report.setReportUserName(loginUser.getRealname());
        }
        if (StringUtils.isNotBlank(report.getWorkfaceId())) {
            DgswrWorkfaceVo workface = dgswrWorkfaceService.queryById(report.getWorkfaceId());
            if (workface != null) {
                report.setProjectId(workface.getProjectId());
                report.setWorkfaceName(workface.getWorkfaceName());
                report.setIntroduction(workface.getIntroduction());
                report.setDetails(workface.getDetails());
            }
        }
        int result = baseMapper.insert(report);

        if (result > 0 && StringUtils.isNotBlank(report.getWorkfaceId())) {
            dgswrWorkfaceService.updateReportStatus(report.getWorkfaceId(), true);
        }

        return result > 0;
    }

    @Override
    public Boolean update(DgswrReportBo bo) {
        DgswrReport report = new DgswrReport();
        BeanUtils.copyProperties(bo, report);

        int result = baseMapper.updateById(report);
        return result > 0;
    }

    @Override
    public Boolean deleteById(String reportId) {
        int result = baseMapper.deleteById(reportId);
        return result > 0;
    }

    @Override
    public Page<DgswrReportVo> selectWorkfaceReportHistory(String workfaceId, Integer pageNo, Integer pageSize) {
        Page<DgswrReport> page = new Page<>(pageNo, pageSize);
        Page<DgswrReport> dgswrReports = baseMapper.selectPage(page, Wrappers.<DgswrReport>lambdaQuery().eq(DgswrReport::getWorkfaceId, workfaceId)
                .orderByDesc(DgswrReport::getCreateTime));
        List<DgswrReport> records = dgswrReports.getRecords();
        Page<DgswrReportVo> pageResult = new Page<>(pageNo, pageSize);
        pageResult.setRecords(BeanUtil.copyToList(records, DgswrReportVo.class));
        pageResult.setTotal(dgswrReports.getTotal());
        return pageResult;
    }

    @Override
    public List<DgswrProjectVo> selectReportList(DgswrReportBo query) {
        List<DgswrProject> dgswrProjects = dgswrProjectMapper.selectList(Wrappers.<DgswrProject>query()
                .lambda()
                .like(StringUtils.isNotBlank(query.getProjectName()), DgswrProject::getProjectName, query.getProjectName())
                .eq(DgswrProject::getStatus, DgswrProjectStatus.ENABLE.getCode()));
        List<DgswrProjectVo> dgswrProjectVos = BeanUtil.copyToList(dgswrProjects, DgswrProjectVo.class);
        for (DgswrProjectVo dgswrProjectVo : dgswrProjectVos) {
            dgswrProjectVo.setWorkfaceList(selectWorkfaceReportList(dgswrProjectVo.getProjectId(), query.getWorkfaceName(), query.getReportDate()));
        }

        return dgswrProjectVos;
    }

    @Override
    public List<DgswrWorkfaceReportVo> selectWorkfaceReportList(String projectId, String workfaceName, String reportDate) {
        List<DgswrWorkface> dgswrWorkfaces = dgswrWorkfaceMapper.selectList(Wrappers.<DgswrWorkface>lambdaQuery()
                .eq(DgswrWorkface::getProjectId, projectId)
                .like(StringUtils.isNotBlank(workfaceName), DgswrWorkface::getWorkfaceName, workfaceName)
                .orderByDesc(DgswrWorkface::getCreateTime));

        List<DgswrWorkfaceVo> workfaceList = BeanUtil.copyToList(dgswrWorkfaces, DgswrWorkfaceVo.class);

        List<DgswrWorkfaceReportVo> resultList = new ArrayList<>();

        for (DgswrWorkfaceVo workface : workfaceList) {
            LambdaQueryWrapper<DgswrReport> wrapper = Wrappers.<DgswrReport>lambdaQuery();
            wrapper.eq(DgswrReport::getWorkfaceId, workface.getWorkfaceId());
            wrapper.apply(StringUtils.isNotBlank(reportDate), "to_char(report_time, 'yyyy-MM-dd') = {0}", reportDate);
            wrapper.orderByDesc(DgswrReport::getReportTime);
            wrapper.last("LIMIT 1");

            DgswrReport latestReport = baseMapper.selectOne(wrapper);

            DgswrWorkfaceReportVo reportVo = new DgswrWorkfaceReportVo();

            // 工作面基本信息
            reportVo.setWorkfaceId(workface.getWorkfaceId());
            reportVo.setWorkfaceName(workface.getWorkfaceName());
            reportVo.setProjectId(workface.getProjectId());
            reportVo.setProjectName(workface.getProjectName());
            reportVo.setAddress(workface.getAddress());
            reportVo.setIntroduction(workface.getIntroduction());
            reportVo.setDetails(workface.getDetails());
            reportVo.setRemark(workface.getRemark());
            reportVo.setIsReported(workface.getIsReported());

            if (latestReport != null) {
                // 有上报记录
                reportVo.setReportContent(latestReport.getReportContent());
                reportVo.setReportImages(latestReport.getReportImages());
                reportVo.setReportUserName(latestReport.getReportUserName());
                reportVo.setReportUnit(latestReport.getReportUnit());
                reportVo.setLatestReportTime(latestReport.getReportTime());
                reportVo.setLatestReportId(latestReport.getReportId());
                reportVo.setProjectName(latestReport.getProjectName());
                reportVo.setWorkfaceName(latestReport.getWorkfaceName());
                reportVo.setIntroduction(latestReport.getIntroduction());
                reportVo.setDetails(latestReport.getDetails());
            }
            resultList.add(reportVo);
        }

        return resultList;
    }

    /**
     * 实体转换为VO
     */
    private DgswrReportVo convertToVo(DgswrReport report) {
        DgswrReportVo vo = new DgswrReportVo();
        BeanUtils.copyProperties(report, vo);
        return vo;
    }
}
