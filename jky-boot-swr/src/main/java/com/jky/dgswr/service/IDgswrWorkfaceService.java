package com.jky.dgswr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.dgswr.domain.DgswrWorkface;
import com.jky.dgswr.domain.bo.DgswrWorkfaceBo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;

import java.util.List;

/**
 * 工作面管理Service接口
 */
public interface IDgswrWorkfaceService extends IService<DgswrWorkface> {

    /**
     * 查询工作面列表
     *
     * @param query 查询条件
     * @return 工作面列表
     */
    List<DgswrWorkfaceVo> selectWorkfaceList(DgswrWorkfaceBo query);

    /**
     * 根据工作面ID查询工作面信息
     *
     * @param workfaceId 工作面ID
     * @return 工作面信息
     */
    DgswrWorkfaceVo queryById(String workfaceId);

    /**
     * 新增工作面
     *
     * @param bo 工作面信息
     * @return 是否成功
     */
    Boolean insert(DgswrWorkfaceBo bo);

    /**
     * 修改工作面
     *
     * @param bo 工作面信息
     * @return 是否成功
     */
    Boolean update(DgswrWorkfaceBo bo);

    /**
     * 删除工作面
     *
     * @param workfaceId 工作面ID
     * @return 是否成功
     */
    Boolean deleteById(String workfaceId);

    /**
     * 根据项目ID查询工作面列表
     *
     * @param projectId 项目ID
     * @return 工作面列表
     */
    List<DgswrWorkfaceVo> selectWorkfaceListByProjectId(String projectId);

    /**
     * 更新工作面上报状态
     *
     * @param workfaceId 工作面ID
     * @param isReported 是否已上报
     * @return 是否成功
     */
    Boolean updateReportStatus(String workfaceId, Boolean isReported);
}
