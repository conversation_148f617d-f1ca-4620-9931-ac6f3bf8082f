package com.jky.dgswr.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.dgswr.domain.DgswrReport;
import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.vo.DgswrProjectVo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceReportVo;

import java.util.List;

/**
 * 上报内容管理Service接口
 */
public interface IDgswrReportService extends IService<DgswrReport> {
    /**
     * 根据上报ID查询上报信息
     *
     * @param reportId 上报ID
     * @return 上报信息
     */
    DgswrReportVo queryById(String reportId);

    /**
     * 新增上报
     *
     * @param bo 上报信息
     * @return 是否成功
     */
    Boolean insert(DgswrReportBo bo);

    /**
     * 修改上报
     *
     * @param bo 上报信息
     * @return 是否成功
     */
    Boolean update(DgswrReportBo bo);

    /**
     * 删除上报
     *
     * @param reportId 上报ID
     * @return 是否成功
     */
    Boolean deleteById(String reportId);

    /**
     * 查询工作面上报列表（包含未上报的工作面）
     *
     * @return 工作面上报列表
     */
    List<DgswrWorkfaceReportVo> selectWorkfaceReportList(String projectId, String workfaceName, String reportDate);

    /**
     * 查询上报列表
     *
     * @param query
     * @return
     */
    List<DgswrProjectVo> selectReportList(DgswrReportBo query);

    /**
     * 查询工作面上报历史记录
     *
     * @param workfaceId
     * @return
     */
    Page<DgswrReportVo> selectWorkfaceReportHistory(String workfaceId, Integer pageNo, Integer pageSize);
}
