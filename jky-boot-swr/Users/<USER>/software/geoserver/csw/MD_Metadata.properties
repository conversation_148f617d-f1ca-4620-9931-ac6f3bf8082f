@fileIdentifier.CharacterString=prefixedName
identificationInfo.MD_DataIdentification.citation.CI_Citation.title.CharacterString=if_then_else(isNull(title), name, title)
identificationInfo.MD_DataIdentification.descriptiveKeywords.MD_Keywords.keyword.CharacterString=keywords
identificationInfo.MD_DataIdentification.abstract.CharacterString=abstract
$dateStamp.Date= if_then_else ( isNull("metadata.date") , 'Unknown', "metadata.date")
hierarchyLevel.MD_ScopeCode.@codeListValue='http://purl.org/dc/dcmitype/Dataset'
$contact.CI_ResponsibleParty.individualName.CharacterString='GeoServer'
distributionInfo.MD_Distribution.transferOptions.MD_DigitalTransferOptions.onLine.CI_OnlineResource%.linkage.URL=list('${url.wfs}','${url.wms}')
distributionInfo.MD_Distribution.transferOptions.MD_DigitalTransferOptions.onLine.CI_OnlineResource%.protocol.CharacterString=list('OGC:WFS','OGC:WMS')
distributionInfo.MD_Distribution.transferOptions.MD_DigitalTransferOptions.onLine.CI_OnlineResource%.name.CharacterString=name
