<wfs>
  <id>WFSInfoImpl-2f01239e:1979ae8cdc2:-7ff9</id>
  <enabled>true</enabled>
  <name>WFS</name>
  <versions>
    <org.geotools.util.Version>
      <version>1.0.0</version>
    </org.geotools.util.Version>
    <org.geotools.util.Version>
      <version>1.1.0</version>
    </org.geotools.util.Version>
    <org.geotools.util.Version>
      <version>2.0.0</version>
    </org.geotools.util.Version>
  </versions>
  <citeCompliant>false</citeCompliant>
  <schemaBaseURL>http://schemas.opengis.net</schemaBaseURL>
  <verbose>false</verbose>
  <gml>
    <entry>
      <version>V_11</version>
      <gml>
        <srsNameStyle>URN</srsNameStyle>
        <overrideGMLAttributes>false</overrideGMLAttributes>
      </gml>
    </entry>
    <entry>
      <version>V_10</version>
      <gml>
        <srsNameStyle>XML</srsNameStyle>
        <overrideGMLAttributes>true</overrideGMLAttributes>
      </gml>
    </entry>
    <entry>
      <version>V_20</version>
      <gml>
        <srsNameStyle>URN2</srsNameStyle>
        <overrideGMLAttributes>false</overrideGMLAttributes>
      </gml>
    </entry>
  </gml>
  <serviceLevel>COMPLETE</serviceLevel>
  <maxFeatures>1000000</maxFeatures>
  <featureBounding>true</featureBounding>
  <canonicalSchemaLocation>false</canonicalSchemaLocation>
  <encodeFeatureMember>false</encodeFeatureMember>
  <hitsIgnoreMaxFeatures>false</hitsIgnoreMaxFeatures>
  <includeWFSRequestDumpFile>true</includeWFSRequestDumpFile>
  <allowGlobalQueries>true</allowGlobalQueries>
  <simpleConversionEnabled>false</simpleConversionEnabled>
  <getFeatureOutputTypeCheckingEnabled>false</getFeatureOutputTypeCheckingEnabled>
</wfs>