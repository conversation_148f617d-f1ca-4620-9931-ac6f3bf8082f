<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.geoserver.org/security/roles" xmlns:gsr="http://www.geoserver.org/security/roles" elementFormDefault="qualified">


    <element name="roleRegistry" type="gsr:RoleRegistryType">
        <key name="RoleKey">
    		<selector xpath="gsr:roleList/gsr:role"/>
    		<field xpath="@id"/>
    	</key>
    	<keyref name="ParentKey" refer="gsr:<PERSON><PERSON><PERSON>">
    		<selector xpath="gsr:roleList/gsr:role"/>
    		<field xpath="@parentID"/>
    	</keyref>
    	<keyref name="UserRoleKey" refer="gsr:RoleKey">
    		<selector xpath="gsr:userList/gsr:userRoles/gsr:roleRef"/>
    		<field xpath="@roleID"/>
    	</keyref>        	    
    	<keyref name="GroupRoleKey" refer="gsr:Role<PERSON><PERSON>">
    		<selector xpath="gsr:groupList/gsr:groupRoles/gsr:roleRef"/>
    		<field xpath="@roleID"/>
    	</keyref>        	        	        	    
    </element>
    
    <complexType name="RoleType">
       	<sequence>
    		<element name="property" type="gsr:RolePropertyType" minOccurs="0" maxOccurs="unbounded"/> 
    	</sequence>    	    
    	<attribute name="id" type="string" use="required"></attribute>
    	<attribute name="parentID" type="string" use="optional"></attribute>
    </complexType>
    
    <complexType name="RoleRefType">
    	<attribute name="roleID" type="string" use="required"></attribute>
    </complexType>
    
    

    <complexType name="RoleRegistryType">
    	<sequence>
    		<element name="roleList" type="gsr:RoleListType" minOccurs="1" maxOccurs="1"></element>
    		<element name="userList" type="gsr:UserRolesType" minOccurs="1" maxOccurs="1"></element>
    		<element name="groupList" type="gsr:GroupRolesType" minOccurs="1" maxOccurs="1"></element>
    	</sequence>
    	<attribute name="version" type="gsr:VersionType" use="required" ></attribute>    	
    </complexType>

    <complexType name="RoleListType">
    	<sequence>
    		<element name="role" type="gsr:RoleType" minOccurs="0" maxOccurs="unbounded"></element>
    	</sequence>
    </complexType>
    
    <complexType name="UserRoleRefListType">
    	<sequence>
    		<element name="roleRef" type="gsr:RoleRefType" minOccurs="0"
    			maxOccurs="unbounded">
    		</element>
    	</sequence>
    	<attribute name="username" type="string" use="required"></attribute>
    </complexType>
    
    <complexType name="GroupRoleRefListType">
    	<sequence>
    		<element name="roleRef" type="gsr:RoleRefType" minOccurs="0"
    			maxOccurs="unbounded">
    		</element>
    	</sequence>
    	<attribute name="groupname" type="string" use="required"></attribute>
    </complexType>
    
    <complexType name="GroupRolesType">
    	<sequence>
    		<element name="groupRoles" type="gsr:GroupRoleRefListType" minOccurs="0"
    			maxOccurs="unbounded">
    		</element>
    	</sequence>
    </complexType>
    
    <complexType name="UserRolesType">
    	<sequence>
    		<element name="userRoles" type="gsr:UserRoleRefListType" minOccurs="0"
    			maxOccurs="unbounded">
    		</element>
    	</sequence>
    </complexType>
    
    <complexType name="RolePropertyType">
    	<simpleContent>
    		<extension base="string">
    			<attribute name="name" type="string" use="required"></attribute>
    		</extension>
    	</simpleContent>
    </complexType>

    <simpleType name="VersionType" >
    	<restriction base="string">
    		<enumeration value="1.0"></enumeration>
    	</restriction>
    </simpleType>
    
            
</schema>