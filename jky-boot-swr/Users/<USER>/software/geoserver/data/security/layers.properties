# rule structure is namespace.layer.operation=role1,role2,...
# namespace: a namespace or * to catch them all (in that case, layer should be *)
# layer: a layer/featureType/coverage name or * to catch them all
# operation: r or w (read or write)
# role list: * to imply all roles, or a list of explicit roles
# The operation will be allowed if the current user has at least one of the
# roles in the rule
*.*.r=*
*.*.w=ADMIN,GROUP_ADMIN
mode=HIDE