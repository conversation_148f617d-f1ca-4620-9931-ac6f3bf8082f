# The format here is service[.method]=ROLE1,...,R<PERSON><PERSON>
# ([method] being optional if you want to apply the rule to all calls to a specific service
# A user can access a service only if he has one of the specified roles
# If not specified in this file, a service or method will be considered unsecured

# Uncomment the following config if you want to test securing WFS service
#wfs.GetFeature=ROLE_WFS_READ
#wfs.Transaction=ROLE_WFS_WRITE
wfs.DropStoredQuery=ADMIN,GROUP_ADMIN
wfs.CreateStoredQuery=ADMIN,GROUP_ADMIN
