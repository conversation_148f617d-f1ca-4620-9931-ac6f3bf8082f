# Default REST security configuration.
# 
# By default this configuration locks down every rest call. The following is an example of a more
# lax configuration in which read only (GET) access is allowed anonymously:
# 
#/**;GET=IS_AUTHENTICATED_ANONYMOUSLY
#/**;POST,DELETE,PUT=ADMIN
#
# The following is an example of a configuration that could be used with the restconfig plugin in 
# which only configuration in a specific workspace is restricted:
#
#/rest/workspaces/topp*;GET=ADMIN
#/rest/workspaces/topp/**;GET=ADMIN
#/**;POST,DELETE,PUT=ADMIN
#
#
/**;GET,HEAD,OPTIONS=ROLE_ADMINISTRATOR
/**;POST,DELETE,PUT=ROLE_ADMINISTRATOR

