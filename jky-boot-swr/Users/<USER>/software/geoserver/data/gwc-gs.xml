<GeoServerGWCConfig>
  <version>1.1.0</version>
  <directWMSIntegrationEnabled>true</directWMSIntegrationEnabled>
  <requireTiledParameter>true</requireTiledParameter>
  <WMSCEnabled>true</WMSCEnabled>
  <TMSEnabled>true</TMSEnabled>
  <securityEnabled>false</securityEnabled>
  <innerCachingEnabled>false</innerCachingEnabled>
  <persistenceEnabled>true</persistenceEnabled>
  <cacheProviderClass>class org.geowebcache.storage.blobstore.memory.guava.GuavaCacheProvider</cacheProviderClass>
  <cacheConfigurations>
    <entry>
      <string>class org.geowebcache.storage.blobstore.memory.guava.GuavaCacheProvider</string>
      <InnerCacheConfiguration>
        <hardMemoryLimit>16</hardMemoryLimit>
        <policy>NULL</policy>
        <concurrencyLevel>4</concurrencyLevel>
        <evictionTime>120</evictionTime>
      </InnerCacheConfiguration>
    </entry>
  </cacheConfigurations>
  <cacheLayersByDefault>true</cacheLayersByDefault>
  <cacheNonDefaultStyles>true</cacheNonDefaultStyles>
  <metaTilingX>4</metaTilingX>
  <metaTilingY>4</metaTilingY>
  <gutter>0</gutter>
  <defaultCachingGridSetIds>
    <string>WebMercatorQuad</string>
    <string>EPSG:4326</string>
    <string>WebMercatorQuadx2</string>
    <string>EPSG:900913</string>
  </defaultCachingGridSetIds>
  <defaultCoverageCacheFormats>
    <string>image/png</string>
    <string>image/jpeg</string>
  </defaultCoverageCacheFormats>
  <defaultVectorCacheFormats>
    <string>application/vnd.mapbox-vector-tile</string>
    <string>image/png</string>
    <string>image/jpeg</string>
  </defaultVectorCacheFormats>
  <defaultOtherCacheFormats>
    <string>application/vnd.mapbox-vector-tile</string>
    <string>image/png</string>
    <string>image/jpeg</string>
  </defaultOtherCacheFormats>
</GeoServerGWCConfig>