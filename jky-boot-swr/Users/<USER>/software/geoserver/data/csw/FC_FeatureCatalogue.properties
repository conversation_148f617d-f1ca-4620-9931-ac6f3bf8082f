$@@id="metadata.custom.object-catalog/uuid"
$\\@uuid="metadata.custom.object-catalog/uuid"
$name.CharacterString=strConcat(name, ' (feature catalogue)')
$scope.CharacterString='geoscientificInformation'
$versionNumber.CharacterString=if_then_else(isNull("metadata.custom.object-catalog/version") , '0', "metadata.custom.object-catalog/version")
$versionDate.DateTime=if_then_else(isNull("metadata.custom.object-catalog/date") , 'Unknown', "metadata.custom.object-catalog/date")
$producer.CI_ResponsibleParty.organisationName.CharacterString='GeoServer'
$featureType.FC_FeatureType.typeName.LocalName=strConcat(name, 'Type')
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.memberName.LocalName="metadata.custom.object-catalog/name"
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.valueType.TypeName.aName.CharacterString="metadata.custom.object-catalog/type"
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.length.CharacterString="metadata.custom.object-catalog/length"
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.definition.CharacterString="metadata.custom.object-catalog/definition"
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.cardinality.Multiplicity.range.MultiplicityRange.lower.Integer="metadata.custom.object-catalog/min-occurence"
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.cardinality.Multiplicity.range.MultiplicityRange.upper.UnlimitedInteger="metadata.custom.object-catalog/max-occurence"
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.cardinality.Multiplicity.range.MultiplicityRange.upper.UnlimitedInteger.@isInfinite=false
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.listedValue%.FC_ListedValue.label.CharacterString="metadata.custom.object-catalog/domain/value"
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.listedValue%.FC_ListedValue.definition.CharacterString="metadata.custom.object-catalog/domain/definition"
featureType.FC_FeatureType.carrierOfCharacteristics%.FC_FeatureAttribute.listedValue%.FC_ListedValue.code.CharacterString="metadata.custom.object-catalog/domain/code"
