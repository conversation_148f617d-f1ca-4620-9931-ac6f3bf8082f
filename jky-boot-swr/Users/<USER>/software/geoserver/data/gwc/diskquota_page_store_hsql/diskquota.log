/*C1*/SET SCHEMA SYSTEM_LOBS
INSERT INTO BLOCKS VALUES(0,2147483647,0)
COMMIT
/*C2*/SET SCHEMA PUBLIC
DISCONNECT
/*C3*/SET SCHEMA PUBLIC
CREATE CACHED TABLE TILESET (\u000a  KEY VARCHAR(320) PRIMARY KEY,\u000a  LAYER_NAME VARCHAR(128),\u000a  GRIDSET_ID VARCHAR(32),\u000a  BLOB_FORMAT VARCHAR(64),\u000a  PARAMETERS_ID VARCHAR(41),\u000a  BYTES NUMERIC(21) DEFAULT 0 NOT NULL\u000a)
CREATE INDEX TILESET_LAYER ON TILESET(LAYER_NAME)
CREATE CACHED TABLE TILEPAGE (\u000a KEY VARCHAR(320) PRIMARY KEY,\u000a TILESET_ID VARCHAR(320) REFERENCES TILESET(KEY) ON DELETE CASCADE,\u000a PAGE_Z SMALLINT,\u000a PAGE_X INTEGER,\u000a PAGE_Y INTEGER,\u000a CREATION_TIME_MINUTES INTEGER,\u000a FREQUENCY_OF_USE FLOAT,\u000a LAST_ACCESS_TIME_MINUTES INTEGER,\u000a FILL_FACTOR FLOAT,\u000a NUM_HITS NUMERIC(64)\u000a)
CREATE INDEX TILEPAGE_TILESET ON TILEPAGE(TILESET_ID, FILL_FACTOR)
CREATE INDEX TILEPAGE_FREQUENCY ON TILEPAGE(FREQUENCY_OF_USE DESC)
CREATE INDEX TILEPAGE_LAST_ACCESS ON TILEPAGE(LAST_ACCESS_TIME_MINUTES DESC)
INSERT INTO TILESET VALUES('___GLOBAL_QUOTA___',NULL,NULL,NULL,NULL,0)
COMMIT
