package org.jeecg.config.sign.interceptor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 签名 拦截器配置
 */
@Configuration
public class SignAuthConfiguration implements WebMvcConfigurer {
    public static String[] urlList = new String[] {"/sys/dict/getDictItems/*", "/sys/dict/loadDict/*",
            "/sys/dict/loadDictOrderByValue/*", "/sys/dict/loadDictItem/*", "/sys/dict/loadTreeData",
            "/sys/api/queryTableDictItemsByCode", "/sys/api/queryFilterTableDictInfo", "/sys/api/queryTableDictByKeys",
            "/sys/api/translateDictFromTable", "/sys/api/translateDictFromTableByKeys"};

    //安全起见 202412 注释了这登记表单的开放url--"/regform/upload/*"
//    public static String[] regformUrl = new String[] {"/regform/outupdate","/regform/outEdit","/regform/outquery","/regform/upload/*","/regform/download/*","/regform/delFile/*","/regform/outQueryEntCollect"};
    public static String[] regformUrl = new String[]{"/regform/outupdate","/regform/outEdit","/regform/outquery","/regform/download/*","/regform/delFile/*","/regform/outQueryEntCollect"};
    @Bean
    public SignAuthInterceptor signAuthInterceptor() {
        return new SignAuthInterceptor();
    }

    @Bean
    public RefererInterceptor refererInterceptor() {return new RefererInterceptor();}

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(signAuthInterceptor()).addPathPatterns(urlList);
        registry.addInterceptor(refererInterceptor()).addPathPatterns(regformUrl);
    }
}
