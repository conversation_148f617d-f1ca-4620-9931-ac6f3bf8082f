package org.jeecg.config.sign.interceptor;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

public class RefererInterceptor extends HandlerInterceptorAdapter {

    List<String> urlList = Arrays.asList("***********".split(","));
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String referer = request.getHeader("referer");
        String host = request.getServerName();
        URL url;
        try {
            url = new java.net.URL(referer);
        } catch (Exception e) {
            // URL解析异常，也置为404
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return false;
        }
        // 判断请求域名和referer域名是否相同
        if (!host.equals(url.getHost())) {
            // 判断是否是白名单
            if (!urlList.contains(url.getHost())) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return false;
            }
        }
        return true;
    }
}
