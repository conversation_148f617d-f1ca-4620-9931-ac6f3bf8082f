package org.jeecg.config.oss;

import org.jeecg.common.util.oss.OssBootUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 云存储 配置
 */
@Configuration
public class OssConfiguration {

    @Value("${jky.oss.endpoint}")
    private String endpoint;
    @Value("${jky.oss.accessKey}")
    private String accessKeyId;
    @Value("${jky.oss.secretKey}")
    private String accessKeySecret;
    @Value("${jky.oss.bucketName}")
    private String bucketName;
    @Value("${jky.oss.staticDomain:}")
    private String staticDomain;


    @Bean
    public void initOssBootConfiguration() {
        OssBootUtil.setEndPoint(endpoint);
        OssBootUtil.setAccessKeyId(accessKeyId);
        OssBootUtil.setAccessKeySecret(accessKeySecret);
        OssBootUtil.setBucketName(bucketName);
        OssBootUtil.setStaticDomain(staticDomain);
    }
}