package com.jky.modules.erp.sale.service.impl;

import com.jky.modules.erp.sale.entity.ErpSaleOutDetail;
import com.jky.modules.erp.sale.mapper.ErpSaleOutDetailMapper;
import com.jky.modules.erp.sale.service.IErpSaleOutDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 销售出库单明细
 * @Author: jky
 * @Date:   2023-01-10
 * @Version: V1.0
 */
@Service
public class ErpSaleOutDetailServiceImpl extends ServiceImpl<ErpSaleOutDetailMapper, ErpSaleOutDetail> implements IErpSaleOutDetailService {
	
	@Autowired
	private ErpSaleOutDetailMapper erpSaleOutDetailMapper;
	
	@Override
	public List<ErpSaleOutDetail> selectByMainId(String mainId) {
		return erpSaleOutDetailMapper.selectByMainId(mainId);
	}
}
