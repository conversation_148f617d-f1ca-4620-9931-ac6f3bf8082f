package com.jky.dgdoc.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2024/05/10
 * @Description: 单体检测报告
 */
@Data
@TableName("dgzjjzj_jcjg_core_sample")
public class DgzjjzjJcjgCoreSample implements Serializable {
    @TableId
    private String id;
    private String customerid;
    private String xmnum;
    private String synum;
    private String xmname;
    private String pdstd;
    private String wtnum;
    private String phnum;
    private String prtnum;
    private String bgbsh;
    private String olwtnum;
    private String wtunit;
    private String wtman;
    private String wtmantel;
    private String gcjianduid;
    private String gccode;
    private String gcname;
    private String gcaddress;
    private String province;
    private String city;
    private String area;
    private String gctype;
    private String kcunit;
    private String sjunit;
    private String jsunit;
    private String sgunit;
    private String jlunit;
    private String jzmanid;
    private String jzman;
    private String sg;
    private String isfirst;
    private String sourceprtnum;
    private String ypname;
    private String ypsample;
    private String facnum;
    private String factory;
    private String batch;
    private String dbnum;
    private String jcparam;
    private String gjname;
    private String symanid;
    private String syman;
    private String symanid1;
    private String syman1;
    private Date wtdate;
    private Date chdate;
    private Date chdateend;
    private String shman;
    private Date shdate;
    private String pzman;
    private Date pzdate;
    private String prman;
    private Date prdate;
    private String qfman;
    private Date qfdate;
    private Date reportdate;
    private String dataip;
    private String istestfinish;
    private String noteip;
    private String note;
    private String beizhu;
    private String qrcode;
    private Date uploadtime;
    private Integer site;
    private Integer takesamplestatus;
    private Integer witnessstatus;
    private String longitude;
    private String latitude;
    private String planno;
    private String itemid;
    private String itemcode;
    private String itemname;
    private String branchid;
    private String branchcode;
    private String branchname;
    private String unitid;
    private String unitcode;
    private String unitname;
    private String labid;
    private String labcode;
    private String labname;
    private String gcid;
    private Date createtime;
    private String handlestatus;
    private String contractcode;
    private Date regdate;
    private String xmid;
    private String gcsub;
    private String gcsubcode;
    private String status;
    private Integer uploadjg;
    private Integer recordnum;
    private Integer modifynum;
    private Date handletime;
    private String bhgfinishman;
    private String bhgfinishmanid;
    private String bhgsubman;
    private String bhgsubmanid;
    private String beizhu2;
    private String qyman;
    private String qymanid;
    private Integer datasources;
    private String dengji;
    private String endky;
    private String signManIdCard;
    private String signManName;
    private String syManIdCard;
    private String checkitems;
    private String checkdetails;
    private String dwaddr;
    private String noticecode;
    private String noticeid;
    private String zjjSubjectId;
    private String zjjSectionId;
    private String zjjBuilderLicense;
    private String zjjSectionCode;
    private String cityId;
    /**
     * 报告文件
     */
    private String reportFileUrl;
    /**
     * 不合格销案情况:1未处理,2处理中,100已销案
     */
    private Integer supDealResult;
    /**
     * 销案时间
     */
    private Date supDealTime;
    private Date jkSyncTime;
}
