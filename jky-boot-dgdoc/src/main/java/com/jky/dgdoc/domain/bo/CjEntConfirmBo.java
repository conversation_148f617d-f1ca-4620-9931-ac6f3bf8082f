package com.jky.dgdoc.domain.bo;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2024/01/11
 * @Description:
 */
@Data
public class CjEntConfirmBo implements Serializable {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 单体ID
     */
    @NotBlank(message = "单体ID不能为空")
    private String monomerId;

    /**
     * 档案编号
     */
    @NotBlank(message = "档案编号不能为空")
    private String archivesNo;

    /**
     * 确认附件
     */
    @NotNull(message = "确认附件不能为空")
    private MultipartFile confirmFile;

}
