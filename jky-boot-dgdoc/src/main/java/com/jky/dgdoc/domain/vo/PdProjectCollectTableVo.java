package com.jky.dgdoc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;

import java.io.Serializable;
import java.util.Date;

/**
 * 获取项目信息对象 pd_project_collect
 */
@Data
public class PdProjectCollectTableVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 项目编号/项目代码
     */
    private String projectNo;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目所在镇（街）ID
     */
    private String projectTownId;
    /**
     * 项目所在镇（街）
     */
    private String projectTown;
    /**
     * 项目详细地址
     */
    private String projectAddress;
    /**
     * 建设单位ID
     */
    private String consUnitId;
    /**
     * 项目类型
     */
    @Dict(dicCode = "dgzjjsp_project_type")
    private String dataType;
    /**
     * 是否装配式建筑
     */
    private String isBuild;
    /**
     * 宗地代码
     */
    private String landNo;
    /**
     * 行政区
     */
    private String xzq;
    /**
     * 项目坐落
     */
    private String xmzl;
    /**
     * 项目总投资（万元）
     */
    private Long xmztz;
    /**
     * 建设单位
     */
    private String consUnit;
    /**
     * 土地面积（平方米）
     */
    private Long tdmj;
    /**
     * 总建筑面积（平方米）
     */
    private Long zjzmj;
    /**
     * 计划开工日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date jhkgrq;
    /**
     * 计划竣工日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date jhjgrq;
    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 土地用途
     */
    private String tdyt;
    /**
     * 建设性质
     */
    private String jsxz;
    /**
     * 建设类别
     */
    private String jslb;
    /**
     * 容积率
     */
    private Long rjl;
    /**
     * 绿地率
     */
    private Long ldl;
    /**
     * 建设规模及内容
     */
    private String jsgmjnr;
    /**
     * 项目状态
     */
    @Dict(dicCode = "dgzjjsp_project_state")
    private String projectState;
    /**
     * 是否重大项目 1.是  0.否
     */
    private String isMajorProject;
    /**
     *
     */
    private Date jkSyncTime;
    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}
