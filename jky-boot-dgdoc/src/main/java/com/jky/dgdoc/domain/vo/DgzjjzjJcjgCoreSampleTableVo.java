package com.jky.dgdoc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/05/10
 * @Description: 单体检测报告
 */
@Data
public class DgzjjzjJcjgCoreSampleTableVo {
    /**
     * 单体ID
     */
    private String monomerId;
    /**
     * 项目ID
     */
    private String zjjSubjectId;
    /**
     * 工程ID
     */
    private String zjjSectionId;
    /**
     * 许可证号
     */
    private String zjjBuilderLicense;
    /**
     * 工程代码
     */
    private String zjjSectionCode;
    /**
     * 检测编号
     */
    private String synum;
    /**
     * 检测项
     */
    private String ypname;
    /**
     * 报告时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reportdate;
    /**
     * 报告文件
     */
    private String reportFileUrl;
    /**
     * 不合格销案情况:1未处理,2处理中,100已销案
     */
    private Integer supDealResult;
    /**
     * 销案时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date supDealTime;
    /**
     * 销案过程
     */
    private List<DgzjjzjSupJcjgUnqualifiedReplyFileVo> supDealProcess;
}
