package com.jky.dgdoc.domain.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2024/01/11
 * @Description: 退回
 */
@Data
public class ReturnBackBo implements Serializable {
    /**
     * 档案实例ID
     */
    private String archivesInstanceId;
    /**
     * 退回原因
     */
    @NotBlank(message = "退回原因不能为空")
    private String returnBackReason;
    /**
     * 原因备注
     */
    private String returnBackRemark;
}
