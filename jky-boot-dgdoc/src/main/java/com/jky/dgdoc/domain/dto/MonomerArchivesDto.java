package com.jky.dgdoc.domain.dto;

import com.jky.dgdoc.domain.vo.DgdocArchivesInstanceVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/01/10
 * @Description: 单体档案集成对象
 */
@Data
public class MonomerArchivesDto implements Serializable {
    /**
     * 单体ID
     */
    private String monomerId;
    /**
     * 单体名称
     */
    private String monomerName;
    /**
     * 单体报告状态 0-合格 1-不合格
     */
    private String monomerReportStatus;
    /**
     * 单体销案状态 0-未销案 1-已销案
     */
    private String monomerSupDealResult;
    /**
     * 单体档案列表
     */
    private List<DgdocArchivesInstanceVo> archives;

}
