package com.jky.dgdoc.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2024/01/04
 * @Description:
 */
@Data
@TableName("dgdoc_archives")
public class DgdocArchives implements Serializable {

    /**
     * 档案ID
     */
    @TableId
    private String archivesId;

    /**
     * 档案父级ID
     */
    private String parentId;

    /**
     * 档案编号
     */
    private String archivesNo;

    /**
     * 档案名称
     */
    private String archivesName;

    /**
     * 档案类型 0-项目档案 1-单体档案
     */
    private String archivesType;

    /**
     * 是否市政档案目录
     */
    private Boolean isSz;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
