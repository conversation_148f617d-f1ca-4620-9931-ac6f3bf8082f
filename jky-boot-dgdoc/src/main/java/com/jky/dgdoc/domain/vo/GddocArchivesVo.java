package com.jky.dgdoc.domain.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/01/04
 * @Description:
 */
@Data
public class GddocArchivesVo {
    /**
     * 档案ID
     */
    private String archivesId;

    /**
     * 档案父级ID
     */
    private String parentId;

    /**
     * 档案编号
     */
    private String archivesNo;

    /**
     * 档案名称
     */
    private String archivesName;

    /**
     * 档案类型 0-项目档案 1-单体档案
     */
    private String archivesType;

    /**
     * 子节点
     */
    private List<GddocArchivesVo> children = new ArrayList<>();
}
