package com.jky.dgdoc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/01/04
 * @Description: 档案实例视图对象
 */
@Data
public class DgdocArchivesInstanceVo implements Serializable {

    /**
     * 档案实例ID
     */
    private String archivesInstanceId;
    /**
     * 项目ID/单体ID
     */
    private String projectMonomerId;
    /**
     * 档案ID
     */
    private String archivesId;
    /**
     * 档案父级ID
     */
    private String parentId;
    /**
     * 档案编号
     */
    private String archivesNo;
    /**
     * 档案名称
     */
    private String archivesName;
    /**
     * 档案类型 0-项目档案 1-单体档案
     */
    private String archivesType;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件名称
     */
    private String fileUrl;
    /**
     * 文件大小
     */
    private BigDecimal fileSize;
    /**
     * 文件页数
     */
    private Integer filePage;
    /**
     * 上传日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date uploadDate;
    /**
     * 上传状态 0-未上传 1-部分上传 2-已上传
     */
    private String uploadState;
    /**
     * 文件状态 0-未归档 1-部分归档 2-已归档
     */
    private String auditState;
    /**
     * 状态 0-正常 1-作废
     */
    private String status;

    /**
     * 退回原因(字典值)
     */
    private String returnReason;
    /**
     * 说明
     */
    private String remark;
    /**
     * 参建方确认附件
     */
    private String cjEntConfirmFile;
    /**
     * 参建方确认状态1-已确认 其他-未确认
     */
    private String cjEntConfirmState;

    /**
     * 子集
     */
    private List<DgdocArchivesInstanceVo> children;

    /**
     * 附件列表 会议照片、现场签到表、验收表、确认表
     */
    private List<DgZjjgMonomerfileVo> fileList = new ArrayList<>();


}
