package com.jky.dgdoc.domain.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2024/01/03
 * @Description:
 */
@Data
public class ProjectEntQuery implements Serializable {
    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String projectId;
    /**
     * 企业类型
     */
    @NotBlank(message = "企业类型不能为空")
    private String entType;

    /**
     * 企业名称
     */
    private String entName;

}
