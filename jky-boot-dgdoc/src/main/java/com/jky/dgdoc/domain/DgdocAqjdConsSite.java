package com.jky.dgdoc.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 工地表（项目表）实体类
 */
// Lombok 注解（需引入依赖）
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
@TableName("dgdoc_aqjd_cons_site")
public class DgdocAqjdConsSite implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 数据主键
     */
    @TableId
    private String id;

    /**
     * 项目安全监督注册号（项目编号）
     */
    private String consSiteSsRegNo;

    /**
     * 工地名称（项目名称）
     */
    private String consSiteName;

    /**
     * 工地地点（项目地点）
     */
    private String consSiteAddr;

    /**
     * 镇街id（所属镇街ID）
     */
    private String townshipId;

    /**
     * 镇街名称（所属镇街）
     */
    private String townshipName;

    /**
     * 建设性质
     */
    private String consProp;

    /**
     * 建设规模/平方米
     */
    private Double consSize;

    /**
     * 建设规模/Km
     */
    private Double consLength;

    /**
     * 合同价格（万元）
     */
    private Double agreementPrice;

    /**
     * 工程造价（万元）
     */
    private Double projectCost;

    /**
     * 监督等级
     */
    private String supLevel;

    /**
     * 投资性质
     */
    private String investNature;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 项目ID（关联项目ID）
     */
    private String projectId;

    /**
     * 合同工期起始时间
     */
    private Date agreementStartTime;

    /**
     * 合同工期结束时间
     */
    private Date agreementEndTime;

    /**
     * 建设单位ID
     */
    private String developOrgId;

    /**
     * 工程类型
     */
    private String projectType;

    /**
     * 特殊类型
     */
    private String itemLabelType;

    /**
     * 重点项目
     */
    private String keyProject;

    /**
     * 项目代码（关联项目代码）
     */
    private String projectNo;

    /**
     * 工地类型
     */
    private String consSiteType;

    /**
     * 建设单位统一社会信用代码
     */
    private String developCreditCode;

    /**
     * 建设单位名称
     */
    private String developOrgName;

    /**
     * 删除标记
     */
    private String signDeleted;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatetime;

    /**
     * 数据全字段MD5
     */
    private String dataMd5;

    /**
     * 数据标识
     */
    private String zzDataFlag;

    /**
     * 数据同步时间（自动更新）
     */
    private Date jkSyncTime;

    @TableField(exist = false)
    private String projectName;

}