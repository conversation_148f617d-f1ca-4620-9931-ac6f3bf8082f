package com.jky.dgdoc.domain.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2024/01/03
 * @Description: 单体工程查询对象
 */
@Data
public class MonomerQuery implements Serializable {
    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String projectId;
    /**
     * 单体名称
     */
    private String monomerName;

}
