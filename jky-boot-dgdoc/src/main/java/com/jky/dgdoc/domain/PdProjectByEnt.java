package com.jky.dgdoc.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 【请填写功能名称】对象 pd_project_collect
 */
@Data
public class PdProjectByEnt {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 项目编号/项目代码
     */
    private String projectNo;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目所在镇（街）ID
     */
    private String projectTownId;
    /**
     * 项目所在镇（街）
     */
    private String projectTown;
    /**
     * 项目详细地址
     */
    private String projectAddress;
    /**
     * 建设单位ID
     */
    private String consUnitId;
    /**
     * 项目类型
     */
    private String dataType;
    /**
     * 是否装配式建筑
     */
    private String isBuild;
    /**
     * 宗地代码
     */
    private String landNo;
    /**
     * 行政区
     */
    private String xzq;
    /**
     * 项目坐落
     */
    private String xmzl;
    /**
     * 项目总投资（万元）
     */
    private Long xmztz;
    /**
     * 建设单位
     */
    private String consUnit;
    /**
     * 土地面积（平方米）
     */
    private Long tdmj;
    /**
     * 总建筑面积（平方米）
     */
    private Long zjzmj;
    /**
     * 计划开工日期
     */
    private Date jhkgrq;
    /**
     * 计划竣工日期
     */
    private Date jhjgrq;
    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 土地用途
     */
    private String tdyt;
    /**
     * 建设性质
     */
    private String jsxz;
    /**
     * 建设类别
     */
    private String jslb;
    /**
     * 容积率
     */
    private Long rjl;
    /**
     * 绿地率
     */
    private Long ldl;
    /**
     * 建设规模及内容
     */
    private String jsgmjnr;
    /**
     * 项目状态
     */
    private String projectState;
    /**
     * 是否重大项目 1.是  0.否
     */
    private String isMajorProject;
    /**
     *
     */
    private Date jkSyncTime;
    /**
     * 创建者
     */
    @TableField(exist = false)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(exist = false)
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Date createTime;

    /**
     * 企业ID
     */
    private String entId;
    /**
     * 企业名称
     */
    private String entName;
    /**
     * 设立时间
     */
    private Date foundDate;
    /**
     * 企业属性
     */
    private String entProperty;
    /**
     * 经济性质
     */
    private String economyProperty;
    /**
     * 主管部门
     */
    private String competentDept;
    /**
     * 联系信息_企业详细地址
     */
    private String entAddress;
    /**
     * 联系信息_邮政编码
     */
    private String zipCode;
    /**
     * 联系信息_联系电话
     */
    private String entPhone;
    /**
     * 联系信息_传真
     */
    private String entFax;
    /**
     * 联系信息_电子邮箱
     */
    private String entEmail;
    /**
     * 工商注册所在地_省
     */
    private String regProvince;
    /**
     * 工商注册所在地_市
     */
    private String regCity;
    /**
     * 工商注册信息_营业执照注册号
     */
    private String licenceNo;
    /**
     * 工商注册信息_营业执照发证机关
     */
    private String licenceIssueDept;
    /**
     * 统一社会信用代码
     */
    private String corporationRegCode;
    /**
     * 统一社会信用代码发证机关
     */
    private String corporationRegDept;
    /**
     * 仅施工企业
     */
    private String safeLicenceNo;
    /**
     * 安全生成许可证主管部门
     */
    private String safeLicenceDept;
    /**
     * 是否具备独立法人资格
     */
    private String isQualification;
    /**
     * 办公地址所在镇
     */
    private String setOfficeTown;
    /**
     * 办公详细地址
     */
    private String setOfficeaddress;
    /**
     * 营业执照注册资本
     */
    private BigDecimal regMoney;
    /**
     * 工人工资支付保函有效期
     */
    private Date salaryValidDate;
    /**
     * 注册资金类型
     */
    private String currency;
    /**
     * 营业执照年检记录
     */
    private Long licenceValidDate;
    /**
     * 资质证注册资本金变更日期
     */
    private Date regmoneyupdate;
    /**
     * 安全生产许可证有效期
     */
    private Date safeValidDate;
    /**
     * 企业净资产(万元)
     */
    private Long entassets;
    /**
     * 营业执照实收资本
     */
    private Long licensemoney;
    /**
     * 在编人员总数
     */
    private Long totalPerson;
    /**
     * 在编技术人员总数
     */
    private Long totalArtPerson;
    /**
     * 租赁合同期限
     */
    private Date validDate;
    /**
     * 核对时间
     */
    private Date checkDate;
    /**
     * 审批时间
     */
    private Date approveDate;
    /**
     * 企业类型
     */
    private String entType;
    /**
     * 备案类型
     */
    private String backupType;
    /**
     * 信用手册公示状态
     */
    private String isView;
    /**
     * 信用手册编号
     */
    private String creditNo;
    /**
     * 手册有效期
     */
    private Date rcValidDate;
    /**
     * 手动设置可以复查
     */
    private String rcSet;
    /**
     * 信用档案状态
     */
    private String canBid;
    /**
     * 经营范围
     */
    private String businessScope;
    /**
     * 招标代理机构/分支机构名称
     */
    private String branchName;
    /**
     * 招标代理机构/分支机构统一社会信用代码
     */
    private String branchCode;
    /**
     * 招标代理机构/分支机构联系电话
     */
    private String branchTel;
    /**
     * 企业曾用名
     */
    private String formerName;
    /**
     * 建档方式
     */
    private String archiveType;
    /**
     * 编辑用户
     */
    private String edituser;
    /**
     * 更新平台编码
     */
    private String updatePlatCode;
    /**
     * 操作部门ID
     */
    private String editdeptId;

}
