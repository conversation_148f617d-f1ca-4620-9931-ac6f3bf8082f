package com.jky.dgdoc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2024/05/10
 * @Description:
 */
@Data
public class DgzjjzjSupJcjgUnqualifiedReplyFileVo {
    /**
     * 主键 ID
     */
    private String id;
    /**
     * 检测编号
     */
    private String syNum;
    /**
     * 附件所属模板的模板名称
     */
    private String moduleFileName;
    /**
     * 附件名称
     */
    private String fileName;
    /**
     * 附件路径
     */
    private String fileUrl;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
}
