package com.jky.dgdoc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgdoc.constants.UserRoleConstants;
import com.jky.dgdoc.domain.*;
import com.jky.dgdoc.domain.dto.SysUserDto;
import com.jky.dgdoc.domain.query.PdProjectCollectQuery;
import com.jky.dgdoc.domain.query.ProjectEntQuery;
import com.jky.dgdoc.domain.query.ProjectQuery;
import com.jky.dgdoc.domain.vo.PdProjectCollectTableVo;
import com.jky.dgdoc.enums.ArchivesType;
import com.jky.dgdoc.enums.ProjectType;
import com.jky.dgdoc.enums.UploadState;
import com.jky.dgdoc.fegin.DgdocApiClient;
import com.jky.dgdoc.mapper.*;
import com.jky.dgdoc.service.IDgdocArchivesInstanceService;
import com.jky.dgdoc.service.IDgdocArchivesService;
import com.jky.dgdoc.service.IDgdocProjectService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2024/01/02/16:49
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class DgdocProjectServiceImpl extends ServiceImpl<DgdocProjectMapper, PdProjectCollect> implements IDgdocProjectService {
    private final DgdocApiClient dgDocApiClient;
    private final DgdocMonomerMapper dgdocMonomerMapper;
    private final DgdocProjectEntMapper dgdocProjectEntMapper;
    private final IDgdocArchivesService archivesService;
    private final IDgdocArchivesInstanceService archivesInstanceService;
    private final DgdocAqjdConsSiteMapper dgdocAqjdConsSiteMapper;
    private final DgdocAqjdConsSiteSubMapper dgdocAqjdConsSiteSubMapper;

    @Autowired
    private final ISysBaseAPI iSysBaseAPI;

    @Override

    public Result<Rs<PdProjectCollect>> queryProject(ProjectQuery query) {
        PdProjectCollect projectCollect = new PdProjectCollect();
        projectCollect.setProjectName(query.getProjectName());
        projectCollect.setProjectId(query.getProjectId());
        //根据当前部门查询
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Set<String> userRolesSet = baseMapper.getUserRolesSet(sysUser.getUsername());
        List<String> targetRoles = Collections.singletonList(UserRoleConstants.ADMIN);
        //如果用户角色不包含管理员，则查询本部门数据
        if (Collections.disjoint(userRolesSet, targetRoles)) {
            String userOrganizationId = baseMapper.getUserOrganizationId(sysUser.getId());
            projectCollect.setOrganizationId(userOrganizationId);
        }
        Result<Rs<PdProjectCollect>> pdProjectCollect = dgDocApiClient.getProjectCollect(projectCollect);
        return pdProjectCollect;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeProject(List<String> ids) {
        List<PdProjectCollect> pdProjectCollects = baseMapper.selectBatchIds(ids);
        for (PdProjectCollect pdProjectCollect : pdProjectCollects) {
            //查询项目下的单体，如果存在不予删除
            List<PdMonomerCollect> projectMonomers = dgdocMonomerMapper.selectList(Wrappers.<PdMonomerCollect>lambdaQuery().eq(PdMonomerCollect::getProjectId, pdProjectCollect.getProjectId()));
            if (!projectMonomers.isEmpty()) {
                throw new JeecgBootException("项目下存在单体，不予删除");
            }
            //查询项目下参建单位，如果存在不予删除
            List<PdProjectEntCollect> projectEntCollects = dgdocProjectEntMapper.selectList(Wrappers.<PdProjectEntCollect>lambdaQuery().eq(PdProjectEntCollect::getProjectId, pdProjectCollect.getProjectId()));
            if (!projectEntCollects.isEmpty()) {
                throw new JeecgBootException("项目下存在参建单位，不予删除");
            }
            //查询项目下档案，如果存在上传的档案不予删除
            List<DgdocArchivesInstance> archivesInstances = archivesInstanceService.list(Wrappers.<DgdocArchivesInstance>lambdaQuery().eq(DgdocArchivesInstance::getProjectMonomerId, pdProjectCollect.getProjectId())
                    .eq(DgdocArchivesInstance::getArchivesType, ArchivesType.PROJECT.getCode()));
            //如果存在已上传的档案，不予删除
            boolean allMatch = archivesInstances.stream().allMatch(a -> a.getUploadState().equals(UploadState.UNUPLOAD.getCode()));
            if (!allMatch) {
                throw new JeecgBootException("项目下存在已上传的档案，不予删除");
            }

        }

        List<String> projectIds = pdProjectCollects.stream().map(PdProjectCollect::getProjectId).collect(Collectors.toList());

        if (!projectIds.isEmpty()) {
            //删除项目档案
            archivesInstanceService.remove(Wrappers.<DgdocArchivesInstance>lambdaQuery().in(DgdocArchivesInstance::getProjectMonomerId, projectIds)
                    .eq(DgdocArchivesInstance::getArchivesType, ArchivesType.PROJECT.getCode()));
            //删除项目参建单位
            dgdocProjectEntMapper.delete(Wrappers.<PdProjectEntCollect>lambdaQuery().in(PdProjectEntCollect::getProjectId, projectIds));

            //删除工地及子表记录
            LambdaQueryWrapper wrappers = new LambdaQueryWrapper<>();
            wrappers.in("project_id",ids);
            List<DgdocAqjdConsSite> dgdocAqidConsSiteList = dgdocAqjdConsSiteMapper.selectList(wrappers);
            List<String> consSiteIds = dgdocAqidConsSiteList.stream().map(DgdocAqjdConsSite::getId).collect(Collectors.toList());

            dgdocAqjdConsSiteMapper.delete(wrappers);

            LambdaQueryWrapper wrappersSub = new LambdaQueryWrapper<>();
            wrappersSub.in("cons_site_id",consSiteIds);
            dgdocAqjdConsSiteSubMapper.delete(wrappersSub);
        }

        return this.removeBatchByIds(ids);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertEnt(PdProjectEntCollect pdProjectEntCollect) {
        //查询参建单位是否存在
        boolean exists = dgdocProjectEntMapper.exists(Wrappers.<PdProjectEntCollect>lambdaQuery()
                .eq(PdProjectEntCollect::getProjectId, pdProjectEntCollect.getProjectId())
                .eq(PdProjectEntCollect::getEntId, pdProjectEntCollect.getEntId()));
        if (exists) {
            throw new JeecgBootException("项目参建单位已存在");
        }
        boolean flag = dgdocProjectEntMapper.insert(pdProjectEntCollect) > 0;
        if (flag) {
            //添加用户信息
            SysUserDto sysUserDto = new SysUserDto();
            sysUserDto.setRealname(pdProjectEntCollect.getEntName());
            sysUserDto.setOrganizationId(pdProjectEntCollect.getCreditCode());
            sysUserDto.setEntType(pdProjectEntCollect.getEntType());
            dgDocApiClient.addDocEnt(sysUserDto);
        }
        return flag;
    }

    @Override
    public Result<Rs<PdProjectEntCollect>> queryEntInfo(ProjectEntQuery query) {
        PdProjectEntCollect pdProjectEntCollect = new PdProjectEntCollect();
        pdProjectEntCollect.setProjectId(query.getProjectId());
        pdProjectEntCollect.setEntType(query.getEntType());
        pdProjectEntCollect.setEntName(query.getEntName());

        Result<Rs<PdProjectEntCollect>> projectEntCollect = dgDocApiClient.getProjectEntCollect(pdProjectEntCollect);
        List<PdProjectEntCollect> result = projectEntCollect.getResult().getList().stream().filter(a -> StringUtils.isNotBlank(a.getEntId())).collect(Collectors.toList());
        projectEntCollect.getResult().setList(result);
        return projectEntCollect;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(PdProjectCollect pdProjectCollect) {
        //判断项目是否存在
        PdProjectCollect projectCollect = this.getOne(Wrappers.<PdProjectCollect>lambdaQuery().eq(PdProjectCollect::getProjectId, pdProjectCollect.getProjectId()));
        if (ObjectUtil.isNotNull(projectCollect)) {
            throw new RuntimeException("项目已存在");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //设置部门ID，以，分割
        List<String> deptIds = baseMapper.getUserDepId(sysUser.getId());
        String depts = String.join(",", deptIds);
        pdProjectCollect.setDeptId(depts);

        boolean save = this.save(pdProjectCollect);
        if (save) {
            //新建项目档案
            List<DgdocArchives> archives;
            //非房建项目
            if (!pdProjectCollect.getDataType().equals(ProjectType.REAL_ESTATE_PROJECT.getCode())) {
                archives = archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.PROJECT.getCode(), true);
            } else {
                archives = archivesService.selectArchivesListByTypeAndIsSz(ArchivesType.PROJECT.getCode(), false);
            }
            if (archives.isEmpty()) {
                throw new JeecgBootException("项目档案未配置，请联系管理员");
            }
            List<DgdocArchivesInstance> archivesInstances = BeanUtil.copyToList(archives, DgdocArchivesInstance.class);
            archivesInstances.forEach(a -> {
                //设置档案项目
                a.setArchivesType(ArchivesType.PROJECT.getCode());
                //设置项目ID
                a.setProjectMonomerId(pdProjectCollect.getProjectId());
            });
            archivesInstanceService.saveBatch(archivesInstances);
        }
        //添加工地信息和工地子表信息
        DgdocAqjdConsSite dgdocAqjdConsSite = new DgdocAqjdConsSite();
        dgdocAqjdConsSite.setProjectId(pdProjectCollect.getProjectId());
        Result<Rs<DgdocAqjdConsSite>> res = dgDocApiClient.getAqjdConsSite(dgdocAqjdConsSite);
        if(res.isSuccess()){
            for(DgdocAqjdConsSite item: res.getResult().getList()){
                if(ObjectUtil.isNull(dgdocAqjdConsSiteMapper.selectById(item.getId()))){
                    dgdocAqjdConsSiteMapper.insert(item);
                }

                DgdocAqjdConsSiteSub dgdocAqjdConsSiteSub = new DgdocAqjdConsSiteSub();
                dgdocAqjdConsSiteSub.setConsSiteId(item.getId());
                Result<Rs<DgdocAqjdConsSiteSub>> sub = dgDocApiClient.getAqjdConsSiteSub(dgdocAqjdConsSiteSub);
                for(DgdocAqjdConsSiteSub subItem: sub.getResult().getList()){
                    if(ObjectUtil.isNull(dgdocAqjdConsSiteSubMapper.selectById(subItem.getId()))){
                        dgdocAqjdConsSiteSubMapper.insert(subItem);
                    }
                }
            }
        }
        return save;
    }

    @Override
    public IPage<PdProjectCollectTableVo> queryList(PdProjectCollectQuery query, Integer pageNo, Integer pageSize) {

        QueryWrapper<PdProjectCollect> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(query.getProjectName()), "p.project_name", query.getProjectName());
        qw.eq(StringUtils.isNotBlank(query.getProjectState()), "p.project_state", query.getProjectState());
        qw.eq(StringUtils.isNotBlank(query.getIsMajorProject()), "p.is_major_project", query.getProjectState());
        qw.orderByDesc("p.create_time");


        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Set<String> userRolesSet = baseMapper.getUserRolesSet(sysUser.getUsername());
        List<String> targetRoles = Arrays.asList(UserRoleConstants.ADMIN, UserRoleConstants.DGDOC_CHECKER, UserRoleConstants.DGDOC_COMMON);
        String userOrganizationId = null;

        IPage<PdProjectCollect> pageList;
        //如果用户角色不包含管理员，则查询本部门数据以及关联参建单位数据
        if (Collections.disjoint(userRolesSet, targetRoles)) {
           /* List<String> deptIds = baseMapper.getUserDepId(sysUser.getId());
            if (CollectionUtil.isEmpty(deptIds)) {
                return new Page<>();
            }
            Iterator<String> iterator = deptIds.iterator();
            while (iterator.hasNext()) {
                String deptId = iterator.next();
                if (iterator.hasNext()) {
                    lqw.eq(PdProjectCollect::getDeptId, deptId).or();
                } else {
                    lqw.eq(PdProjectCollect::getDeptId, deptId);
                }
            }*/

            userOrganizationId = baseMapper.getUserOrganizationId(sysUser.getId());

            if (StringUtils.isBlank(userOrganizationId)) {
                return new Page<>();
            }
            pageList = baseMapper.pageList(new Page<>(pageNo, pageSize), query, userOrganizationId);
        } else {
            LambdaQueryWrapper<PdProjectCollect> lqw = new LambdaQueryWrapper<>();
            lqw.like(StringUtils.isNotBlank(query.getProjectName()), PdProjectCollect::getProjectName, query.getProjectName());
            lqw.eq(StringUtils.isNotBlank(query.getProjectState()), PdProjectCollect::getProjectState, query.getProjectState());
            lqw.eq(StringUtils.isNotBlank(query.getIsMajorProject()), PdProjectCollect::getIsMajorProject, query.getProjectState());
            lqw.orderByDesc(PdProjectCollect::getCreateTime);
            pageList = baseMapper.selectPage(new Page<>(pageNo, pageSize), lqw);
        }
        return pageList.convert(e -> BeanUtil.toBean(e, PdProjectCollectTableVo.class));
    }
}
