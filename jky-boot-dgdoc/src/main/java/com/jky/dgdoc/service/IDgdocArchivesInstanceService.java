package com.jky.dgdoc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.dgdoc.domain.DgdocArchivesInstance;
import com.jky.dgdoc.domain.bo.CjEntConfirmBo;
import com.jky.dgdoc.domain.query.ArchivesProcessQuery;
import com.jky.dgdoc.domain.query.ArchivesQuery;
import com.jky.dgdoc.domain.vo.DgdocArchivesInstanceVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 档案实例接口
 */
public interface IDgdocArchivesInstanceService extends IService<DgdocArchivesInstance> {

    /**
     * 查询档案实例列表
     *
     * @param query
     * @return
     */
    List<DgdocArchivesInstanceVo> treeList(ArchivesQuery query);

    /**
     * 审核
     *
     * @param archivesInstanceId
     * @return
     */
    Boolean audit(List<String> archivesInstanceId);

    /**
     * 退回
     *
     * @param archivesInstanceId
     * @return
     */
    Boolean returnBack(String archivesInstanceId);

    /**
     * 上传档案
     *
     * @param archivesInstanceId
     * @param request
     * @return
     */
    Boolean upload(String archivesInstanceId, HttpServletRequest request);

    /**
     * 预览
     *
     * @param archivesInstanceId
     * @param response
     */
    void preview(String archivesInstanceId, HttpServletResponse response);

    /**
     * 作废/恢复
     *
     * @param archivesInstanceId
     * @return
     */
    Boolean updateStatus(String archivesInstanceId, String status);

    /**
     * 删除档案文件
     *
     * @param archivesInstanceId
     * @return
     */
    Boolean removeFile(String archivesInstanceId);

    /**
     * 查询项目验收进度
     *
     * @param query
     * @return
     */
    Object processList(ArchivesProcessQuery query);

    /**
     * 参建方确认
     *
     * @param bo
     * @return
     */
    Boolean cjEntConfirm(CjEntConfirmBo bo);
}
