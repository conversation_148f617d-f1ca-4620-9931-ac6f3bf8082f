package com.jky.dgdoc.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgdoc.domain.DgdocArchives;
import com.jky.dgdoc.mapper.DgdocArchivesMapper;
import com.jky.dgdoc.service.IDgdocArchivesService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 档案接口
 */
@Service
public class DgdocArchivesServiceImpl
        extends ServiceImpl<DgdocArchivesMapper, DgdocArchives> implements IDgdocArchivesService {

    @Override
    public List<DgdocArchives> selectArchivesListByTypeAndIsSz(String archivesType, Boolean isSz) {
        return baseMapper.selectList(Wrappers.<DgdocArchives>lambdaQuery()
                .eq(DgdocArchives::getArchivesType, archivesType)
                .eq(DgdocArchives::getIsSz, isSz));
    }

    @Override
    public List<DgdocArchives> selectArchivesListByType(String archivesType) {
        return this.list(Wrappers.<DgdocArchives>lambdaQuery().eq(DgdocArchives::getArchivesType, archivesType));
    }
}
