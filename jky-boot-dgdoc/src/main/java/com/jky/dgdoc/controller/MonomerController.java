package com.jky.dgdoc.controller;

import com.jky.dgdoc.domain.PdMonomerCollect;
import com.jky.dgdoc.domain.PdMonomerEntCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.bo.MonomerBo;
import com.jky.dgdoc.domain.query.MonomerEntQuery;
import com.jky.dgdoc.domain.query.MonomerQuery;
import com.jky.dgdoc.domain.vo.DgzjjzjJcjgCoreSampleTableVo;
import com.jky.dgdoc.domain.vo.MonomerVo;
import com.jky.dgdoc.domain.vo.PdMonomerCollectTableVo;
import com.jky.dgdoc.service.IDgdocMonomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 单体管理
 */
@Validated
@Api(value = "单体管理", tags = {"单体管理"})
@RestController
@RequestMapping("/monomer")
@RequiredArgsConstructor
public class MonomerController {
    private final IDgdocMonomerService dgdocMonomerService;


    /**
     * 搜索单体信息
     */
    @ApiOperation("搜索单体信息")
    @GetMapping
    public Result<List<PdMonomerCollect>> queryMonomerList(@Validated MonomerQuery query) {
        Result<List<PdMonomerCollect>> result = new Result<>();

        Result<Rs<PdMonomerCollect>> monomerList = dgdocMonomerService.queryMonomerList(query);
        if (!monomerList.isSuccess()) {
            result.error500(monomerList.getMessage());
            return result;
        }
        result.setResult(monomerList.getResult().getList());
        return result;
    }

    /**
     * 查询单体列表
     */
    @ApiOperation("查询单体列表(dgdoc-monomer-list)")
    @RequiresPermissions("dgdoc-monomer-list")
    @GetMapping("/list/{projectId}")
    public Result<List<PdMonomerCollectTableVo>> list(@ApiParam("项目ID") @NotBlank(message = "项目ID不能为空") @PathVariable("projectId") String projectId) {
        Result<List<PdMonomerCollectTableVo>> result = new Result<>();

        result.setResult(dgdocMonomerService.queryList(projectId));
        return result;
    }

    /**
     * 查询单体详细信息
     */
    @ApiOperation("查询单体详细信息(dgdoc-monomer-query)")
    @RequiresPermissions("dgdoc-monomer-query")
    @GetMapping("/{monomerId}")
    public Result<MonomerVo> queryMonomer(@ApiParam("单体ID") @NotBlank(message = "单体ID不能为空") @PathVariable("monomerId") String monomerId) {
        Result<MonomerVo> result = new Result<>();

        MonomerVo monomer = dgdocMonomerService.queryById(monomerId);
        result.setResult(monomer);
        return result;
    }

    /**
     * 查询单体检测报告
     */
    @ApiOperation("查询单体检测报告(dgdoc-monomer-list)")
    @RequiresPermissions("dgdoc-monomer-list")
    @GetMapping("/report/{monomerId}")
    public Result<List<DgzjjzjJcjgCoreSampleTableVo>> queryMonomerReport(@ApiParam("单体ID") @NotBlank(message = "单体ID不能为空") @PathVariable("monomerId") String monomerId) {
        Result<List<DgzjjzjJcjgCoreSampleTableVo>> result = new Result<>();
        List<DgzjjzjJcjgCoreSampleTableVo> reportList = dgdocMonomerService.queryReportById(monomerId);
        result.setResult(reportList);
        return result;
    }


    /**
     * 新增项目单体
     */
    @ApiOperation("新增项目单体(dgdoc-monomer-add)")
    @RequiresPermissions("dgdoc-monomer-add")
    @PostMapping
    public Result<PdMonomerCollect> add(@RequestBody MonomerBo bo) {
        Result<PdMonomerCollect> result = new Result<>();

        if (dgdocMonomerService.insert(bo)) {
            result.success("添加成功");
        } else {
            result.error500("添加失败");
        }
        return result;
    }

    /**
     * 修改项目单体
     */
    @ApiOperation("修改项目单体(dgdoc-monomer-update)")
    @RequiresPermissions("dgdoc-monomer-update")
    @PutMapping
    public Result<PdMonomerCollect> update(@RequestBody MonomerBo bo) {
        Result<PdMonomerCollect> result = new Result<>();

        if (dgdocMonomerService.update(bo)) {
            result.success("修改成功");
        } else {
            result.error500("修改失败");
        }
        return result;
    }

    /**
     * 删除项目单体
     */
    @ApiOperation("删除项目单体(dgdoc-monomer-remove)")
    @RequiresPermissions("dgdoc-monomer-remove")
    @DeleteMapping("/{monomerIds}")
    public Result<Void> remove(@ApiParam("单体ID") @NotEmpty(message = "单体ID不能为空") @PathVariable("monomerIds") String[] monomerIds) {
        Result<Void> result = new Result<>();

        if (dgdocMonomerService.remove(Arrays.asList(monomerIds))) {
            result.success("删除成功");
        } else {
            result.error500("删除失败");
        }
        return result;
    }


    /**
     * 查询单体参建单位
     */
    @ApiOperation("查询单体参建单位")
    @GetMapping("/ent")
    public Result<List<PdMonomerEntCollect>> queryEntList(@Validated MonomerEntQuery query) {
        Result<List<PdMonomerEntCollect>> result = new Result<>();
        Result<Rs<PdMonomerEntCollect>> monomerList = dgdocMonomerService.queryEntList(query);
        if (!monomerList.isSuccess()) {
            result.error500(monomerList.getMessage());
            return result;
        }
        result.setResult(monomerList.getResult().getList());
        return result;
    }

}
