<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.dgdoc.mapper.DgdocProjectMapper">


    <select id="pageList" resultType="com.jky.dgdoc.domain.PdProjectCollect"
            parameterType="com.jky.dgdoc.domain.query.PdProjectCollectQuery">
        select p.*
        from dgdoc_project p
        inner join (select pe.project_id
        from dgdoc_project_ent pe
        <where>
            <if test="creditCode != null and creditCode != ''">
                credit_code = #{creditCode}
            </if>
        </where>
        group by pe.project_id) pe
        on p.project_id = pe.project_id
        <where>
            <if test="ew.projectName != null and ew.projectName != ''">
                and p.project_name like concat('%', #{ew.projectName}, '%')
            </if>
            <if test="ew.projectState != null and ew.projectState != ''">
                and p.project_state = #{ew.projectState}
            </if>
            <if test="ew.isMajorProject != null and ew.isMajorProject != ''">
                and p.is_major_project = #{ew.isMajorProject}
            </if>
        </where>
    </select>
</mapper>