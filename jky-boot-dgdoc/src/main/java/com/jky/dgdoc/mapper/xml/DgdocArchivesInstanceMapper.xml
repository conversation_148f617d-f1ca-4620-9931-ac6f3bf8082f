<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.dgdoc.mapper.DgdocArchivesInstanceMapper">


    <select id="queryList" resultType="com.jky.dgdoc.domain.vo.DgdocArchivesInstanceVo">
        select dgai.*, dgzp.state as cjEntConfirmState
        from dgdoc_archives_instance dgai
        left join dg_zjjg_patroldata dgzp on dgai.archives_instance_id = dgzp.system_project_id and dgzp.state = '1'
        <where>
            AND dgai.project_monomer_id = #{ew.projectMonomerId}
            AND dgai.archives_type = #{ew.archivesType}
            <if test="ew.status != null and ew.status != ''">
                AND dgai.status = #{ew.status}
            </if>
        </where>
    </select>
    <select id="queryFileList" resultType="com.jky.dgdoc.domain.vo.DgZjjgMonomerfileVo">
        select *
        from dg_zjjg_monomerfile
        where archives_instance_id = #{archivesInstanceId}
        order by jk_sync_time desc
    </select>
</mapper>