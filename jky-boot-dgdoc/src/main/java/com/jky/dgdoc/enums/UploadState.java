package com.jky.dgdoc.enums;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2024/01/08
 * @Description:
 */
@Getter
public enum UploadState {
    /**
     * 未上传
     */
    UNUPLOAD("0", "未上传"),
    /**
     * 部分上传
     */
    PARTUPLOAD("1", "部分上传"),
    /**
     * 已上传
     */
    UPLOADED("2", "已上传");

    private String code;
    private String name;

    UploadState(String code, String name) {
        this.code = code;
        this.name = name;
    }

}
