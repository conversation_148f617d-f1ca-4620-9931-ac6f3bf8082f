package com.jky.dgdoc.enums;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2024/06/12
 * @Description: 单体数据来源
 */
@Getter
public enum MonomerDataSource {
    /**
     * 1-施工许可
     */
    CONSTRUCTION_PERMIT("1", "施工许可"),
    /**
     * 2-提前介入
     */
    ADVANCE_INTERVENTION("2", "提前介入"),
    /**
     * 3-无效
     */
    INVALID("3", "无效"),
    /**
     * 4-由项目生成
     */
    GENERATED_BY_PROJECT("4", "由项目生成");

    private String code;
    private String name;

    MonomerDataSource(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
