package com.jky.dgdoc.enums;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2024/05/11
 * @Description: 单体销案状态
 */
@Getter
public enum MonomerSupDealResult {
    //0-未销案 1-已销案
    NOT_CLOSED("0", "未销案"),
    CLOSED("1", "已销案");

    private final String code;
    private final String desc;

    MonomerSupDealResult(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
