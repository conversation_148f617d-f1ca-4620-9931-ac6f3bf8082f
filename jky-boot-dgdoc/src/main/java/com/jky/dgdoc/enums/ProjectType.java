package com.jky.dgdoc.enums;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2024/01/09
 * @Description:
 */
@Getter
public enum ProjectType {

    /**
     * 1-重点工程项目（已取消）
     */
    KEY_PROJECT("1", "重点工程项目（已取消）"),
    /**
     * 2-截污工程项目
     */
    INTERCEPT_PROJECT("2", "截污工程项目"),
    /**
     * 3-其他工程项目
     */
    OTHER_PROJECT("3", "其他工程项目"),
    /**
     * 4-既有建筑装饰装修工程
     */
    DECORATION_PROJECT("4", "既有建筑装饰装修工程"),
    /**
     * 5-简易低风险项目
     */
    SIMPLE_PROJECT("5", "简易低风险项目"),
    /**
     * 6-房地产项目
     */
    REAL_ESTATE_PROJECT("6", "房地产项目"),
    /**
     * 7-工业类项目
     */
    INDUSTRIAL_PROJECT("7", "工业类项目"),
    /**
     * 8-其他房建工程
     */
    OTHER_BUILDING_PROJECT("8", "其他房建工程");


    private String code;
    private String name;

    ProjectType(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
