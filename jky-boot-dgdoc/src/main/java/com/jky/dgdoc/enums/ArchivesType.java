package com.jky.dgdoc.enums;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2024/01/09
 * @Description:
 */
@Getter
public enum ArchivesType {

    /**
     * 项目档案
     */
    PROJECT("0", "项目档案"),
    /**
     * 单体档案
     */
    MONOMER("1", "单体档案");

    private String code;
    private String name;

    ArchivesType(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
