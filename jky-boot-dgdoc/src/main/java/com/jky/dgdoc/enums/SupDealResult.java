package com.jky.dgdoc.enums;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2024/05/11
 * @Description: 销案状态
 */
@Getter
public enum SupDealResult {
    //不合格销案情况:1未处理,2处理中,100已销案
    NOT_CLOSED(1, "未处理"),
    PROCESSING(2, "处理中"),
    CLOSED(100, "已销案");

    private final Integer code;
    private final String desc;

    SupDealResult(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
