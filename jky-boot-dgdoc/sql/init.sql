-- 项目表
CREATE TABLE gddoc_project
(
    project_id       VARCHAR(64) COMMENT '项目ID',
    project_no       VARCHAR(255) COMMENT '项目编号/项目代码',
    project_name     VARCHAR(255) COMMENT '项目名称',
    project_town_id  VARCHAR(255) COMMENT '项目所在镇（街）ID',
    project_town     VARCHAR(255) COMMENT '项目所在镇（街）',
    project_address  VARCHAR(255) COMMENT '项目详细地址',
    cons_unit_id     VARCHAR(255) COMMENT '建设单位ID',
    data_type        VARCHAR(255) COMMENT '项目类型',
    is_build         VARCHAR(255) COMMENT '是否装配式建筑',
    land_no          VARCHAR(255) COMMENT '宗地代码',
    xzq              VARCHAR(255) COMMENT '行政区',
    xmzl             VARCHAR(255) COMMENT '项目坐落',
    xmztz            DECIMAL(10, 2) COMMENT '项目总投资（万元）',
    cons_unit        VARCHAR(255) COMMENT '建设单位',
    tdmj             BIGINT COMMENT '土地面积（平方米）',
    zjzmj            BIGINT COMMENT '总建筑面积（平方米）',
    jhkgrq           DATE COMMENT '计划开工日期',
    jhjgrq           DATE COMMENT '计划竣工日期',
    data_state       VARCHAR(255) COMMENT '数据状态',
    tdyt             VARCHAR(255) COMMENT '土地用途',
    jsxz             VARCHAR(255) COMMENT '建设性质',
    jslb             VARCHAR(255) COMMENT '建设类别',
    rjl              DECIMAL(10, 2) COMMENT '容积率',
    ldl              DECIMAL(10, 2) COMMENT '绿地率',
    jsgmjnr          VARCHAR(255) COMMENT '建设规模及内容',
    project_state    VARCHAR(255) COMMENT '项目状态',
    is_major_project VARCHAR(1) COMMENT '是否重大项目 1.是  0.否',
    jk_sync_time     TIMESTAMP COMMENT '',
    dept_id          VARCHAR(64) COMMENT '部门ID',
    create_by        VARCHAR(255) COMMENT '创建者',
    update_by        VARCHAR(255) COMMENT '更新者',
    create_time      TIMESTAMP COMMENT '创建时间',
    update_time      TIMESTAMP COMMENT '更新时间',
    primary key (project_id)
);
-- 项目单位表
CREATE TABLE gddoc_project_ent
(
    project_ent_id     VARCHAR(64) COMMENT '项目单位ID',
    project_id         VARCHAR(64) COMMENT '项目id',
    ent_type           CHAR(2) COMMENT '企业类型',
    ent_id             VARCHAR(64) COMMENT '企业id',
    ent_name           VARCHAR(255) COMMENT '企业名称',
    credit_code        VARCHAR(255) COMMENT '企业统一社会信用代码',
    aptitude_grade     VARCHAR(64) COMMENT '企业资质等级',
    aptitude_cer_no    VARCHAR(255) COMMENT '企业资质证书编号',
    pro_leader         VARCHAR(64) COMMENT '项目负责人',
    pro_leader_idcard  VARCHAR(255) COMMENT '项目负责人证件号',
    pro_leader_cer_no  VARCHAR(255) COMMENT '负责人证书编号',
    pro_leader_mobile  VARCHAR(64) COMMENT '负责人联系电话',
    pro_leader_reg_no  VARCHAR(255) COMMENT '负责人注册执业资格证号',
    pro_leader_safe_no VARCHAR(255) COMMENT '负责人安全生产考核合格证号',
    ent_legal          VARCHAR(64) COMMENT '企业法定代表人',
    ent_legal_mobile   VARCHAR(64) COMMENT '企业法定代表人联系电话',
    data_state         CHAR(1) COMMENT '数据状态, 0：无效、1：有效',
    jk_sync_time       TIMESTAMP COMMENT '',
    create_by          VARCHAR(255) COMMENT '创建者',
    update_by          VARCHAR(255) COMMENT '更新者',
    create_time        TIMESTAMP COMMENT '创建时间',
    update_time        TIMESTAMP COMMENT '更新时间',
    primary key (project_ent_id)
);
-- 单体表
CREATE TABLE gddoc_monomer
(
    monomer_id         VARCHAR(64) COMMENT '单体工程信息表ID',
    monomer_name       VARCHAR(255) COMMENT '单体工程名称',
    project_id         VARCHAR(64) COMMENT '项目ID',
    licencemoney       VARCHAR(255) COMMENT '许可证号',
    licencedate        DATE COMMENT '施工许可日期',
    pro_plan_cer_no    VARCHAR(255) COMMENT '工规证号',
    is_finish          VARCHAR(255) COMMENT '是否竣工验收',
    finish_date        DATE COMMENT '竣工验收日期',
    is_finish_backup   VARCHAR(255) COMMENT '是否竣工验收备案',
    finish_backup_no   VARCHAR(255) COMMENT '竣工验收备案证书号',
    finish_backup_date DATE COMMENT '竣工验收备案日期',
    is_stope           VARCHAR(255) COMMENT '是否终止监督',
    stope_date         DATE COMMENT '终止监督日期',
    township_name      VARCHAR(255) COMMENT '镇区名称',
    data_source        CHAR(1) COMMENT '单体工程数据来源',
    data_id            VARCHAR(64) COMMENT '单体工程数据来源表主键',
    data_state         CHAR(1) COMMENT '数据状态 0-无效 1-有效',
    gczt               CHAR(1) COMMENT '工程状态',
    gclb               VARCHAR(255) COMMENT '工程类别',
    lddm               VARCHAR(255) COMMENT '楼栋代码',
    aj_num             VARCHAR(255) COMMENT '安全监督登记号',
    zj_num             VARCHAR(255) COMMENT '质量监督登记号',
    plan_start_time    DATE COMMENT '计划开始时间',           -- 计划开始时间
    plan_end_time      DATE COMMENT '计划完成时间',           -- 计划完成时间
    licence_file       VARCHAR(255) COMMENT '施工许可证附件', -- 附件
    gcghxk_no          VARCHAR(255) COMMENT '工程规划许可证号',
    jk_sync_time       TIMESTAMP COMMENT '同步时间',
    create_by          VARCHAR(255) COMMENT '创建者',
    update_by          VARCHAR(255) COMMENT '更新者',
    create_time        TIMESTAMP COMMENT '创建时间',
    update_time        TIMESTAMP COMMENT '更新时间',
    primary key (monomer_id)
);
-- 单体单位表
CREATE TABLE gddoc_monomer_ent
(
    monomer_ent_id     VARCHAR(64) COMMENT '单体单位ID',
    project_id         VARCHAR(64) COMMENT '项目id',
    monomer_id         VARCHAR(64) COMMENT '单体工程ID',
    ent_type           CHAR(2) COMMENT '企业类型',
    ent_id             VARCHAR(64) COMMENT '企业id',
    ent_name           VARCHAR(255) COMMENT '企业名称',
    credit_code        VARCHAR(255) COMMENT '企业统一社会信用代码',
    aptitude_grade     VARCHAR(64) COMMENT '企业资质等级',
    aptitude_cer_no    VARCHAR(255) COMMENT '企业资质证书编号',
    pro_leader         VARCHAR(64) COMMENT '项目负责人',
    pro_leader_idcard  VARCHAR(255) COMMENT '项目负责人证件号',
    pro_leader_cer_no  VARCHAR(255) COMMENT '负责人证书编号',
    pro_leader_mobile  VARCHAR(64) COMMENT '负责人联系电话',
    pro_leader_reg_no  VARCHAR(255) COMMENT '负责人注册执业资格证号',
    pro_leader_safe_no VARCHAR(255) COMMENT '负责人安全生产考核合格证号',
    ent_legal          VARCHAR(64) COMMENT '企业法定代表人',
    ent_legal_mobile   VARCHAR(64) COMMENT '企业法定代表人联系电话',
    data_state         CHAR(1) COMMENT '数据状态, 0：无效、1：有效',
    jk_sync_time       TIMESTAMP COMMENT '',
    create_by          VARCHAR(255) COMMENT '创建者',
    update_by          VARCHAR(255) COMMENT '更新者',
    create_time        TIMESTAMP COMMENT '创建时间',
    update_time        TIMESTAMP COMMENT '更新时间',
    primary key (monomer_ent_id)
);
-- 档案目录结构表
create table gddoc_archives
(
    archives_id   varchar(64) comment '档案ID',
    parent_id     varchar(64) comment '档案父级ID',
    archives_no   varchar(255) comment '档案编号',
    archives_name varchar(255) comment '档案名称',
    archives_type char(1) comment '档案类型 0-项目档案 1-单体档案',
    create_by     varchar(64) comment '创建者',
    update_by     varchar(64) comment '更新者',
    create_time   TIMESTAMP comment '创建时间',
    update_time   TIMESTAMP comment '更新时间',
    primary key (archives_id)
);
-- 档案实例表
CREATE TABLE gddoc_archives_instance
(
    archives_instance_id VARCHAR(64) COMMENT '档案实例ID',
    project_id           VARCHAR(64) COMMENT '项目ID',
    monomer_id           VARCHAR(64) COMMENT '单体工程ID',
    archives_id          varchar(64) comment '档案ID',
    parent_id            varchar(64) comment '档案父级ID',
    archives_no          varchar(255) comment '档案编号',
    archives_name        varchar(255) comment '档案名称',
    archives_type        char(1) comment '档案类型 0-项目档案 1-单体档案',
    file_type            varchar(64) COMMENT '文件类型',
    file_url             VARCHAR(255) COMMENT '文件地址',
    file_size            numeric COMMENT '文件大小',
    file_page            int COMMENT '文件页数',
    upload_date          DATE COMMENT '上传日期',
    upload_state         CHAR(1) default '0' COMMENT '上传状态 0-未上传 1-部分上传 2-已上传',
    audit_state          CHAR(1) default '0' COMMENT '审核状态 0-未审核 1-审核通过 2-审核不通过',
    status               CHAR(1) default '0' COMMENT '状态 0-正常 1-作废',
    return_back_reason   CHAR(1) COMMENT '退回原因',
    return_back_remark   VARCHAR(255) COMMENT '退回备注',
    create_by            VARCHAR(64) COMMENT '创建者',
    update_by            VARCHAR(64) COMMENT '更新者',
    create_time          TIMESTAMP COMMENT '创建时间',
    update_time          TIMESTAMP COMMENT '更新时间',
    primary key (archives_instance_id)
);










