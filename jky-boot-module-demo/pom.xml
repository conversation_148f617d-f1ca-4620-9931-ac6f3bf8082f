<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jky-boot-parent</artifactId>
        <groupId>com.jky.boot</groupId>
        <version>3.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jky-boot-module-demo</artifactId>

    <properties> <!-- 增加这部分，避免pom.xm文件报错 -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
		<skipTests>true</skipTests>
	</properties>

    <dependencies>
        <dependency>
            <groupId>com.jky.boot</groupId>
            <artifactId>jky-boot-base-core</artifactId>
        </dependency>
        <!--流程模块-->
        <dependency>
            <groupId>com.jky.boot</groupId>
            <artifactId>jky-boot-module-flowable</artifactId>
            <version>3.1</version>
        </dependency>
        <!--引入微服务启动依赖 starter
      <dependency>
          <groupId>org.jeecgframework.boot</groupId>
          <artifactId>jky-boot-starter-cloud</artifactId>
      </dependency>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jky-boot-starter-job</artifactId>
        </dependency>-->
    </dependencies>

</project>